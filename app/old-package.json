{"name": "fme-app", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "buffer": "^6.0.3", "fs": "^0.0.1-security", "google-auth-library": "^8.7.0", "googleapis": "^110.0.0", "react": "^18.2.0", "react-audio-voice-recorder": "^1.0.4", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "browser": {"child_process": false}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}