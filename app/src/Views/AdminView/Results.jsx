import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react';
import { Dialog, DialogContent, DialogTitle, IconButton, MenuItem, Select, Button } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

function Results({ category, index }) {
    const [open, setOpen] = useState(false);
    const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
    const currentYear = new Date().getFullYear();
    const years = Array.from({ length: currentYear - 2022 }, (_, i) => 2023 + i);
    const [downloadType, setDownloadType] = useState(null);

    const handleClickOpen = (type) => {
        setDownloadType(type);
        setOpen(true);
    };

    const handleClose = () => {
        setOpen(false);
    };

    const handleDownload = () => {
        if (!downloadType) return;
        const url = `/api/${downloadType}?categoryId=${category._id}&year=${selectedYear}&format=csv`;
        window.location.href = url;
        setOpen(false);
    };

    return (
        <div className='p-4 w-full border border-dullGray grid grid-cols-[1fr_1fr_1fr_1fr_1fr] items-center gap-4 w-full hover:bg-dullGray' key={index}>
            <div className="flex items-center font-normal text-black text-sm">{category.name}</div>
            <div className="flex items-center font-normal text-black text-sm">
                <button onClick={() => handleClickOpen('getWinners')}>Download CSV</button>
            </div>
            {category.hasSoloists ? (
                <div className="flex items-center font-normal text-black text-sm">
                    <button onClick={() => handleClickOpen('getSoloists')}>Download CSV</button>
                </div>
            ) : (
                <div className='flex items-center font-normal text-black text-sm'></div>
            )}
            <div className='flex items-center font-normal text-black text-sm'>
                <a href={`/api/getWinners?categoryId=${category._id}&year=${currentYear}&format=html`}>
                    Generate HTML for WP Site
                </a>
            </div>
            
            <Dialog open={open} onClose={handleClose}>
                <DialogTitle>
                    Select Year
                    <IconButton onClick={handleClose} style={{ position: 'absolute', right: 8, top: 8 }}>
                        <CloseIcon />
                    </IconButton>
                </DialogTitle>
                <DialogContent>
                    <Select
                        value={selectedYear}
                        onChange={(e) => setSelectedYear(e.target.value)}
                        fullWidth
                    >
                        {years.map(year => (
                            <MenuItem key={year} value={year}>{year}</MenuItem>
                        ))}
                    </Select>
                    <Button onClick={handleDownload} variant="contained" color="primary" style={{ marginTop: '16px' }}>
                        Download
                    </Button>
                </DialogContent>
            </Dialog>
        </div>
    );
}

export default observer(Results);
