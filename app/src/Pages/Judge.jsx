import { useState, useEffect, useContext } from "react";
import axios from "axios";
import WorkView from "../Views/WorkView";
import GroupView from "../Views/GroupView";
import React from "react";
import VideoRecorder from "../Components/WorkViewComponents/VideoRecorder";
import AppHeader from "../Components/Header/Header";
import { useSearchParams } from "react-router-dom";
import { IoMdClose } from "react-icons/io";
import { AiOutlinePlusCircle } from "react-icons/ai";
import { AppProviderStore } from "../AppStore";
import { observer } from 'mobx-react';
import { toJS } from "mobx";
import LogRocket from 'logrocket';
import JudgeSidebar from "../Components/Sidebar/JudgeSidebar";
import JudgeSummary from "../Components/JudgeSummary/JudgeSummary";


function Judge() {
  const { AppStore } = useContext(AppProviderStore);
  const {
    selectedGroup, setGroup, groups, setGroups,
    judgeIntros, setJudgeIntros,
    menuSelected, setSelectedMenu,
    judge, setJudge,
    categoryId, setCategoryId,
    category, setCategory,
    assignments, setAssignments,
    classifications, setClassifications,
    judgeVideoCreated, setJudgeVideoCreated,
    videoSubmitted, setVideoSubmitted,
    isBanner, setIsBanner
  } = AppStore;

  const [queryParameters] = useSearchParams();
  const [webcamPresent, setWebcamPresent] = useState();
  const judgeId = queryParameters.get("judgeId"); // Move this here

  let query = atob(queryParameters.get("data"));
  console.log("what did he do", queryParameters.toString());

  useEffect(() => {
    async function getList() {
      if (!judgeId) return; // Early return if no judgeId

      console.log('getLists')
      const response = await axios(
        `/api/judges/${judgeId}/getAssignments`
      );
      const judgeData = await response.data;
      await setJudge(judgeData);
      await setAssignments(judgeData.assigned || []);
      let assignedGroups = [];
      if (judgeData.assigned) {
        for(const assignment of judgeData.assigned){
          for(const assignedClass of assignment.classifications){
            if(assignedClass.groups.length){
              assignedGroups.push(...assignedClass.groups)
            }
          }
        }
      }
      setGroups(assignedGroups);
      LogRocket.identify(judgeData._id, judgeData.name);
      let hasWebcam = checkWebCam();
      setWebcamPresent(hasWebcam);
      if (judgeData?.judgeIntro) {
        setJudgeVideoCreated(true);
      }
    }

    if (!judge?._id && judgeId) {
      getList();
    }
  }, [judge, judgeId, setJudge, setAssignments, setGroups, setWebcamPresent, setJudgeVideoCreated]);  // Add proper dependencies

  // async function handleVideoSubmit(video){
  //   console.log(video)
  //   let judgeIntro = {
  //     judge: judge,
  //     video: video
  //   }

  //   let response = await axios.post(`/api/categories/judgeIntro?id=${categoryId}`,judgeIntro)
  //   console.log(response)
  //   setVideoSubmitted(true)
  // }

  function handleGroupSelect(group) {
    console.log("Judge: Selected group: ", group._id);
    console.log("Judge: Current AppStore.selectedGroup: ", AppStore.selectedGroup?._id);
    setGroup(group);
    console.log("Judge: After setGroup, AppStore.selectedGroup: ", AppStore.selectedGroup?._id);
  }


  function checkVirtual(label) {
    let virtualLabels = ["OBS-Camera", "Intel Virtual Camera", "XSplit", "Twitch Virtual Cam"]
    return virtualLabels.some((name) => {
      console.log(label, name, label.includes(name))
      return label.includes(name)
    })
  }
  function checkWebCam() {
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
      // Enumerate the media devices
      navigator.mediaDevices.enumerateDevices()
        .then(function (devices) {
          console.log(devices)
          var hasWebcam = devices.some(function (device) {
            return device.kind === 'videoinput' && !checkVirtual(device.label);
          });

          if (hasWebcam) {
            console.log('has webcam')
            return true
            // Do something if webcam is present
          } else {
            console.log('no webcam')
            return false
            // Do something if webcam is not present
          }
        })
        .catch(function (err) {
          console.error('Error enumerating devices: ', err);
        });
    } else {
      console.error('getUserMedia is not supported');
    }
  }

  return (
    <>
      {judgeVideoCreated ? (
        <div className="main_app main_app h-full min-h-[100vh]">
          <AppHeader />
          <div className="grid grid-cols-[270px_1fr] pt-[90px] h-full grid-rows-1 min-h-[100vh]">
            {assignments && (
              <JudgeSidebar
                handleSelect={handleGroupSelect}
                selectedGroup={selectedGroup}
                assignments={assignments}
              />
            )}
            <div className="p-5 bg-mainContent main_content overflow-auto">
              {selectedGroup._id ? (
                <>
                  {console.log('Judge: Rendering WorkView with group:', selectedGroup._id)}
                  <WorkView group={selectedGroup} />
                </>
              ) : (
                <>
                  {console.log('Judge: Rendering JudgeSummary, no selected group')}
                  <JudgeSummary handleSelect={handleGroupSelect} />
                </>
              )}
            </div>
          </div>
        </div>
      ) : (
        <div className="app_loader absolute top-0 left-0 w-full bg-loader h-full bg-cover bg-no-repeat bg-top">
          <div className="bg-loaderOpacity h-full">
            {isBanner ? (
              <div className="w-2/5 xxl:w-[500px] m-auto h-full flex flex-col justify-center">
                <div className="h-fit shadow-white shadow-xl overflow-hidden rounded-3xl transition-all duration-200 ease-linear hover:scale-[1.1] ">
                  <div className="bg-violin bg-cover bg-no-repeat p-5 pb-36">
                    <div className="rounded-2xl backdrop-blur-3xl bg-transparent p-4 w-fit">
                      <p className="text-white font-semibold">The Foundation</p>
                      <p className="text-iconGreen font-semibold">for Music Education</p>
                    </div>
                  </div>
                  <button
                    className="p-5 w-full bg-iconGreen flex flex-row items-center justify-center text-center text-white font-semibold flex items-center"
                    onClick={() => setIsBanner(false)}
                  >
                    Add an Introduction&nbsp;
                    <AiOutlinePlusCircle color="#ffffff" size={20} />
                  </button>
                </div>
              </div>
            ) : (
              <>
                <button
                  className="p-5 w-full bg-iconGreen flex flex-row items-center justify-center text-center text-white font-semibold flex items-center"
                  onClick={() => setJudgeVideoCreated(true)}
                >
                  Skip&nbsp;
                  <AiOutlinePlusCircle color="#ffffff" size={20} />
                </button>
                <div className="w-1/3 m-auto h-full flex flex-col justify-center">
                  <VideoRecorder judge={judge} category={category} hasWebcam={webcamPresent} />
                </div>
              </>
            )}
          </div>
        </div>
      )}
    </>

  );
}

export default observer(Judge);
