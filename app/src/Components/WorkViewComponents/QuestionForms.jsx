import classNames from "classnames";
import DebugBreadcrumb from "../TestComponents/DebugBreadcrumb";
import { useContext, useState, useEffect } from 'react';
import { AppProviderStore } from "../../AppStore";
import { observer } from 'mobx-react';
import { toJS } from "mobx";
import { TextField, Checkbox, FormControlLabel, FormGroup, Typography } from '@mui/material';

const QuestionForm = () => {
  const { AppStore } = useContext(AppProviderStore);
  const currentJudgeId = AppStore.judge._id;

  // Get the current judge's assignment details
  const getCurrentJudgeAssignment = () => {
    console.log('appstore', toJS(AppStore));

    // Get the current category and classification from AppStore
    const currentCategory = AppStore.currentSelectedCategory;
    const currentClassification = AppStore.currentSelectedClassification;

    console.log('Current category from AppStore:', toJS(currentCategory));
    console.log('Current classification from AppStore:', toJS(currentClassification));

    // If we have both category and classification in AppStore, use them
    if (currentCategory && currentClassification) {
      return {
        phase: currentClassification.phase,
        category: AppStore.category
      };
    }

    // If we don't have the information in AppStore, log a warning
    console.warn('Missing category or classification information in AppStore');
    return {
      phase: null,
      category: AppStore.category
    };
  };

  // Load existing judge score
  useEffect(() => {
    const loadJudgeScore = () => {
      const group = AppStore.selectedGroup;
      if (!group) return;

      let judgeScore;
      if (group.classifications.length) {
        const classification = group.classifications[0]; // or whichever classification you're working with
        judgeScore = classification.judgeScores?.find(score => score.judgeId === currentJudgeId);
      } else {
        judgeScore = group.judgeScores?.find(score => score.judgeId === currentJudgeId);
      }

      if (judgeScore) {
        AppStore.setJudgeScore(judgeScore);
      } else {
        // Set default score state if no existing score
        AppStore.setJudgeScore({
          judgeId: currentJudgeId,
          score: null,
          movesOn: false,
          isCommended: false,
          isNational: false,
          isCitation: false,
          isState: false
        });
      }
    };

    loadJudgeScore();
  }, [AppStore.selectedGroup, currentJudgeId]);

  const assignment = getCurrentJudgeAssignment();
  console.log(assignment)
  const isPhase1 = assignment?.phase === 1;
  console.log(isPhase1)
  const isCitationCategory = AppStore.category?.name?.toLowerCase().includes('citation');

  const handleScoreUpdate = (value, field) => {
    if (AppStore.judgeScore) {
      AppStore.handleScoreUpdate(value, field);
    } else {
      console.warn('Cannot update score: No judge score available');
    }
  };

  // Determine if Soloist Notes should be displayed
  const displaySoloistNotes = AppStore.currentTrack?.soloists?.length > 0 && AppStore.currentTrack?.contest !== '64345c8fdd98890aad8cef3d';
  console.log('soloists', displaySoloistNotes)
  console.log('whats the score: ', AppStore.judgeScore ? toJS(AppStore.judgeScore.score) : 'No score available')
  // Get notes for the current judge or create a new entry if not present
  const getMemoByJudge = (memos, judgeId) => {
    console.log('getMemoByJudge ', toJS(memos))
    if (!Array.isArray(memos)) {
      memos = [];
    }
    let memo = memos.find(m => m.judge === judgeId);
    if (!memo) {
      memo = { judge: judgeId, content: '' };
      memos.push(memo);
    }
    return memo;
  };

  // Initialize memos if they don't exist
  if (AppStore.currentTrack && !AppStore.currentTrack.memos) {
    AppStore.currentTrack.memos = {
      notes: [],
      audioMemos: [],
      soloistNotes: []
    };
  }

  const notesMemo = getMemoByJudge(AppStore.currentTrack?.memos?.notes || [], currentJudgeId);
  const soloistNotesMemo = getMemoByJudge(AppStore.currentTrack?.memos?.soloistNotes || [], currentJudgeId);
  // Local state for text area content
  const [notesContent, setNotesContent] = useState(notesMemo.content);
  const [soloistNotesContent, setSoloistNotesContent] = useState(soloistNotesMemo.content);

  useEffect(() => {
    setNotesContent(notesMemo.content);
  }, [notesMemo.content]);

  useEffect(() => {
    setSoloistNotesContent(soloistNotesMemo.content);
  }, [soloistNotesMemo.content]);

  return (
    <div className="bg-white">
      <DebugBreadcrumb breadcrumbs={"/Components/WorkViewComponents/QuestionForms"} />
      <div className="bg-header px-4 py-3">
        <h2 className="text-white font-semibold">Scoring & Notes</h2>
      </div>
      <div className="p-4">
        <div className={classNames("bg-listBorderColor rounded-md p-3 mb-4")}>
          <p className="text-iconGreen font-semibold">Summary Comments:</p>
          <div className="flex flex-row justify-start gap-4 mt-2 mb-2 items-center">
            <textarea
              rows={3}
              className="peer block min-h-[auto] w-full rounded border-0 bg-white px-3 py-[0.32rem] leading-[1.6] outline-none transition-all duration-200 ease-linear focus:placeholder:opacity-100 data-[te-input-state-active]:placeholder:opacity-100 motion-reduce:transition-none dark:text-neutral-200 dark:placeholder:text-neutral-200 [&:not([data-te-input-placeholder-active])]:placeholder:opacity-0 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
              value={notesContent}
              onBlur={() => { notesMemo.content = notesContent; AppStore.saveTrack(); }}
              onChange={(e) => { setNotesContent(e.target.value); }}
            />
          </div>
        </div>

        {/* Conditional rendering of Soloist Notes */}
        {/* {displaySoloistNotes && (
          <div className={classNames("bg-listBorderColor rounded-md p-3 mb-4")}>
            <p className="text-iconGreen font-semibold">Soloist Notes:</p>
            <div className="flex flex-row justify-start gap-4 mt-2 mb-2 items-center">
              <textarea
                rows={3}
                className="peer block min-h-[auto] w-full rounded border-0 bg-white px-3 py-[0.32rem] leading-[1.6] outline-none transition-all duration-200 ease-linear focus:placeholder:opacity-100 data-[te-input-state-active]:placeholder:opacity-100 motion-reduce:transition-none dark:text-neutral-200 dark:placeholder:text-neutral-200 [&:not([data-te-input-placeholder-active])]:placeholder:opacity-0 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                value={soloistNotesContent}
                onBlur={() => { soloistNotesMemo.content = soloistNotesContent; AppStore.saveTrack(); }}
                onChange={(e) => { setSoloistNotesContent(e.target.value); }}
              />
            </div>
          </div>
        )} */}
        {/**
         * ask rick about this later.
         * */}
        {displaySoloistNotes && (
  <div className={classNames("bg-listBorderColor rounded-md p-3 mb-4")}>
    <p className="text-iconGreen font-semibold">Outstanding Soloists:</p>
    <div className="flex flex-row justify-start gap-4 mt-2 mb-2 items-center">
      <div className="text-neutral-700 dark:text-neutral-200">
        <p className="font-semibold">Soloists To Choose From:</p>
        <ul>
          {AppStore.currentTrack.soloists.map((soloist, index) => (
            <li key={index}>
              {soloist.instrument}
              {soloist.timeStamp && ` (starting at ${soloist.timeStamp})`}
            </li>
          ))}
        </ul>
      </div>
    </div>
    <div className="flex flex-row justify-start gap-4 mt-2 mb-2 items-center">
      <textarea
        rows={3}
        className="peer block min-h-[auto] w-full rounded border-0 bg-white px-3 py-[0.32rem] leading-[1.6] outline-none transition-all duration-200 ease-linear focus:placeholder:opacity-100 data-[te-input-state-active]:placeholder:opacity-100 motion-reduce:transition-none dark:text-neutral-200 dark:placeholder:text-neutral-200 [&:not([data-te-input-placeholder-active])]:placeholder:opacity-0 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
        value={soloistNotesContent}
        onBlur={() => { soloistNotesMemo.content = soloistNotesContent; AppStore.saveTrack(); }}
        onChange={(e) => { setSoloistNotesContent(e.target.value); }}
      />
    </div>
  </div>
)}
        {/* Old score */}
        {/* <div className={classNames("bg-listBorderColor  rounded-md p-3 mb-0")}>
          <p className="text-iconGreen font-semibold">Group Score</p>
          <div className="flex flex-row justify-start gap-4 mt-2 mb-2 items-center">
            <input class="w-full p-4 h-4 text-blue-600 bg-gray-100 border-gray-300  outline-none transition-all duration-200 ease-linear focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
              type="number" value={AppStore.score} onBlur={AppStore.saveOldGroupScore} onChange={(e) => { AppStore.seOldtGroupScore(e.target.value); }} />
          </div>
        </div> */}

        <div className={classNames("bg-listBorderColor rounded-md p-3 mb-4")}>
          <Typography variant="subtitle1" className="text-iconGreen font-semibold mb-4">
            Scoring:
          </Typography>

          <FormGroup className="flex flex-col gap-3">
            <div>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Score
              </Typography>
              <TextField
                type="number"
                fullWidth
                size="small"
                value={AppStore.judgeScore?.score || ""}
                onChange={(e) => handleScoreUpdate(e.target.value, 'score')}
                variant="outlined"
              />
            </div>

            {isPhase1 ? (
              <FormControlLabel
                control={
                  <Checkbox
                    checked={AppStore.judgeScore?.movesOn || false}
                    onChange={(e) => handleScoreUpdate(e.target.checked, 'movesOn')}
                    sx={{
                      color: '#008544',
                      '&.Mui-checked': {
                        color: '#008544',
                      },
                    }}
                  />
                }
                label="Moves On to Phase 2"
              />
            ) : (
              <>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={AppStore.judgeScore?.isCommended || false}
                      onChange={(e) => handleScoreUpdate(e.target.checked, 'isCommended')}
                      sx={{
                        color: '#008544',
                        '&.Mui-checked': {
                          color: '#008544',
                        },
                      }}
                    />
                  }
                  label="Commended Performance"
                />

                {!isCitationCategory && (
                  <>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={AppStore.judgeScore?.isNational || false}
                          onChange={(e) => handleScoreUpdate(e.target.checked, 'isNational')}
                          sx={{
                            color: '#008544',
                            '&.Mui-checked': {
                              color: '#008544',
                            },
                          }}
                        />
                      }
                      label="National Winner"
                    />
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={AppStore.judgeScore?.isState || false}
                          onChange={(e) => handleScoreUpdate(e.target.checked, 'isState')}
                          sx={{
                            color: '#008544',
                            '&.Mui-checked': {
                              color: '#008544',
                            },
                          }}
                        />
                      }
                      label="State Winner"
                    />
                  </>
                )}

                {isCitationCategory && (
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={AppStore.judgeScore?.isCitation || false}
                        onChange={(e) => handleScoreUpdate(e.target.checked, 'isCitation')}
                        sx={{
                          color: '#008544',
                          '&.Mui-checked': {
                            color: '#008544',
                          },
                        }}
                      />
                    }
                    label="Citation Award"
                  />
                )}
              </>
            )}
          </FormGroup>
        </div>
      </div>
    </div>
  );
};

export default observer(QuestionForm);
