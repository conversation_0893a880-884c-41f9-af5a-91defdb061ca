import React, { Fragment, useContext, useState, useEffect } from "react";
import { FaMusic } from "react-icons/fa";
import { TbPlayerTrackNext, TbPlayerTrackPrev } from "react-icons/tb";
import DebugBreadcrumb from "../TestComponents/DebugBreadcrumb";
import { AppProviderStore } from "../../AppStore";
import { observer } from 'mobx-react';
import * as albumArt from 'album-art';
import { toJS } from "mobx";

const AudioPlayer = ({ track, buttons, changeTrack, group }) => {
  let [art, setArt] = useState("");
  const { AppStore } = useContext(AppProviderStore);
  const { user, isRecording } = AppStore; // Destructure isRecording from AppStore

  // Adjusting minioSrc if it exists
  console.log('audioplayer', track)
  if (track?.minioSrc && track.minioSrc.includes('s3.yobo.dev')) {
    let src = track.minioSrc;
    track.minioSrc = src.replace("s3.yobo.dev", "app.foundationformusiceducation.org");
  }

  // Effect for fetching album art
  // useEffect(() => {
  //   let isMounted = true;
  //   albumArt(track.composer).then((res) => {
  //     if (isMounted) {
  //       setArt(res);
  //     }
  //   });
  //   return () => {
  //     isMounted = false;
  //   };
  // }, [track.composer]);

  // Function to render based on whether track is available
  const renderPlayer = () => {
    if (!track) {
      return <Fragment />;
    }

    // Determine if next and prev buttons should be disabled
    const isDisabled = isRecording;

    return (
      <div className={`bg-white ${track.minioSrc ? 'p-4' : 'py-3 px-4'} grid ${track.minioSrc ? 'grid-cols-[1fr_1.5fr]' : 'grid-cols-[1fr_1fr]'} gap-5 items-center justify-between mb-5`}>
        <DebugBreadcrumb breadcrumbs={"/Components/WorkViewComponents/AudioPlayer"} />
        <div className="flex items-center gap-5">
          {art ? (
            <img src={art} className="w-[60px] h-[60px]" style={{ borderRadius: "5px" }} />
          ) : (
            <FaMusic color="#032741" className="bg-[#d7d7d7] p-3" size={60} />
          )}
          <div>
            <h3 className="text-iconGreen font-semibold mb-1">
              {track.title} - {track.composer}
            </h3>
            <h4 className="text-black text-xs font-semibold">{group.name}</h4>
          </div>
        </div>
        <div className="flex gap-4 items-center justify-between">
          {track.minioSrc ? (
            <audio
              id="track-player"
              className="w-3/4"
              src={track.minioSrc}
              controls
              controlsList="nodownload noplaybackrate"
            />
          ) : (
            <div className="w-3/4 flex items-center gap-2">
              <FaMusic className="text-gray-400" size={12} />
              <p className="text-gray-500 text-xs">No audio file available</p>
            </div>
          )}
          <div className="flex gap-3">
            {buttons.prev && (
              <button onClick={() => changeTrack(-1)} disabled={isDisabled}>
                <TbPlayerTrackPrev
                  color="#008544"
                  size={30}
                  className={`text-iconGreen border-2 rounded-full p-1 ${
                    isDisabled ? "opacity-50 cursor-not-allowed" : "border-iconGreen"
                  }`}
                />
              </button>
            )}
            {buttons.next && (
              <button onClick={() => changeTrack(1)} disabled={isDisabled}>
                <TbPlayerTrackNext
                  color="#008544"
                  size={30}
                  className={`text-iconGreen border-2 rounded-full p-1 ${
                    isDisabled ? "opacity-50 cursor-not-allowed" : "border-iconGreen"
                  }`}
                />
              </button>
            )}
          </div>
        </div>
      </div>
    );
  };

  return renderPlayer();
};

export default observer(AudioPlayer);
