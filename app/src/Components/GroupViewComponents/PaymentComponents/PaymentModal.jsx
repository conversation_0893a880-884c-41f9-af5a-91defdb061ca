import React from 'react';
import { Modal, Box, Typography } from '@mui/material';
import { Elements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import PaymentPage from './PaymentPage';

const stripePromise = loadStripe(process.env.REACT_APP_STRIPE_PUBLIC_KEY);

const PaymentModal = ({ open, handleClose }) => {
  return (
    <Modal open={open} onClose={handleClose}>
      <Box
        sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          backgroundColor: 'white',
          maxWidth: 900,
          width: '100%',
          maxHeight: '90vh', // Set maximum height to 90% of viewport height
          overflowY: 'auto', // Enable vertical scrolling
          margin: 'auto',
          p: 4,
          borderRadius: 2,
          boxShadow: 3,
          '&:focus': {
            outline: 'none', // Remove focus outline
          },
        }}
      >
        <Elements stripe={stripePromise}>
          <PaymentPage handleClose={handleClose} />
        </Elements>
      </Box>
    </Modal>
  );
};

export default PaymentModal;