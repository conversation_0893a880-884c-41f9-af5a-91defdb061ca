import { useState, useContext } from "react";
import { AppProviderStore } from "../AppStore";
import { observer } from 'mobx-react';
import axios from "axios";
import Swal from 'sweetalert2';
import { useNavigate } from "react-router-dom";
import {jwtDecode} from "jwt-decode";


function Login({ toggleForm }) {
  const { AppStore } = useContext(AppProviderStore);
  const { setUser } = AppStore;
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const navigate = useNavigate();

  const handleLoginSubmit = () => {
    axios.post('/api/login', { email, password })
      .then(response => {
        console.log('response')
        console.log('login response', response)
        console.log(response.data)
        if (response.data) {
          localStorage.setItem('token', response.data);
          const user = jwtDecode(response.data).returnUser
          console.log(user)
          setUser(user);
          navigate("/account")

          // Perform any additional actions on successful login if needed
        }
      })
      .catch(error => {
        if(error.response.data.message === "user") {
          console.log('user error')
          Swal.fire({
            icon: 'error',
            title: 'Oops...',
            text: 'User not found! Double check your email or register',
          });
        }else if(error.response.data.message === "password"){
          Swal.fire({
            icon: 'error',
            title: 'Oops...',
            text: 'Password is incorrect. Reminder your password is atleast 8 characters, with atleast 1 number, capital letter and symbol',
          });
        }else{
          Swal.fire({
            icon: 'eror',
            title: "Issue Logging in",
            text: "There was an issue trying to log in"
          })
        }
      });
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      handleLoginSubmit();
    }
  };
 
  const handleForgotPassword = () => {
    if (email) {
      // If email is already filled out, use it for forgot password
      axios.post('/api/forgotPassword', { email })
        .then(response => {
          // Handle response if needed
          Swal.fire({
            icon: 'success',
            title: 'Password reset email sent',
            text: 'Please check your email for further instructions.',
          });
        })
        .catch(error => {
          // Handle error if needed
          console.error('Error sending forgot password email:', error);
          Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'An error occurred while sending the forgot password email. Please try again later.',
          });
        });
    } else {
      // If email is not filled out, prompt the user to input an email
      Swal.fire({
        input: 'email',
        inputPlaceholder: 'Enter your email address',
        title: 'Forgot Password',
        text: 'Please enter your email address to reset your password:',
        confirmButtonText: 'Submit',
        showCancelButton: true,
        cancelButtonText: 'Cancel',
        preConfirm: (email) => {
          if (!email) {
            Swal.showValidationMessage('Email address is required');
          }
          return email;
        }
      }).then((result) => {
        if (result.isConfirmed) {
          // Handle forgot password with the provided email
          const userEmail = result.value;
          axios.post('/api/forgotPassword', { email: userEmail })
            .then(response => {
              // Handle response if needed
              Swal.fire({
                icon: 'success',
                title: 'Password reset email sent',
                text: 'Please check your email for further instructions.',
              });
            })
            .catch(error => {
              // Handle error if needed
              console.error('Error sending forgot password email:', error);
              Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'An error occurred while sending the forgot password email. Please try again later.',
              });
            });
        }
      });
    }
  };

  return (
    <div className="p-5 w-full flex flex-col gap-5 items-center justify-center text-center">
      <input
        type="email"
        placeholder="Enter your email"
        className="p-3 border-0 w-full rounded border"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
      />
      <input
        type="password"
        placeholder="Enter your password"
        className="p-3 border-0 w-full rounded border"
        value={password}
        onChange={(e) => setPassword(e.target.value)}
        onKeyDown={handleKeyPress}
      />
      <button
        className="p-5 w-1/2 bg-iconGreen text-white font-semibold rounded-3xl"
        onClick={handleLoginSubmit}
      >
        Login
      </button>
      <button
        className="p-5 w-1/2 bg-red-500 text-white font-semibold rounded-3xl"
        onClick={handleForgotPassword}
      >
        Forgot Password?
      </button>
      <p className="text-white cursor-pointer" onClick={toggleForm}>
        Don't have an account? Register here.
      </p>
    </div>
  );
}

export default observer(Login);
