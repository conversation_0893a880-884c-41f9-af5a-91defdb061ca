import mongoose from "mongoose";
const { Schema, model } = mongoose;



const soloistSchema = new Schema({
  name: {
    type: String,
    required: true,
  },
  instrument: {
    type: String,
    required: true,
  },
  timeStamp:{
    type: String
  }
});

const memoSchema = new Schema({
  judge: {
    type: Schema.Types.ObjectId,
    ref: "Judges",
  },
  content: String,
});

const trackSchema = new Schema({
  title: {
    type: String,
    required: true,
  },
  composer: {
    type: String,
    required: true,
  },
  venue: {
    type: String,
    required: true,
    default: 'N/A'
  },
  dateRecorded: {
    type: Date
  },
  duration:{
    type: String,
  },
  trackSrc: {
    type: String,
  },
  minioSrc:{
    type: String,
  },
  soloists: [soloistSchema],
  memos: {
    audioMemos: {
      type: [memoSchema],
      default: [],
    },
    notes: {
      type: [memoSchema],
      default: [],
    },
    soloistNotes: {
      type: [memoSchema],
      default: [],
    },
  },
});

const Track = model("Tracks", trackSchema);
export default Track;
