import { useF<PERSON>, Controller } from 'react-hook-form';
import { <PERSON><PERSON>ield, Button, Typography, Box } from '@mui/material';
import { observer } from 'mobx-react';
import { useContext } from 'react';
import Swal from "sweetalert2";
import { AppProviderStore } from '../../../AppStore';


function EditEntryForm({ defaultValues, route, id, handleClose }) {
    const { register, handleSubmit, control, formState: { errors, irtyFields, touchedFields } } = useForm({ defaultValues });
    const {AppStore} = useContext(AppProviderStore)
    const {selectedGroup, updateSelectedGroup} = AppStore
    const onSubmit = async (data) => {
        let entryUpdate = {
            filter: {_id:selectedGroup._id},
            toUpdate:{}
        }
        let toSet = {}
        for ( const key in data){

            if(touchedFields[key]){
                toSet[key] = data[key]
            }
        }
        if (Object.keys(toSet).length !== 0) {
            entryUpdate.toUpdate.$set = { ...toSet }
        }
        try{
            let updateSuccessful = await updateSelectedGroup(entryUpdate)
            if(updateSuccessful){
                handleClose()
                Swal.fire({
                    title: "Success!",
                    text:"Your Entry Info has been updated successfully",
                    icon:"success"
                })
            }else{
                handleClose()
                Swal.fire({
                    title: "Error",
                    text: "There was an error updating your entry information",
                    html: 'Please contact <a href="mailto:<EMAIL>"><EMAIL></a> if problem persists',
                    confirmButtonText: 'Ok' ,
                    icon: 'error'
               })
            }
        }catch(err){
            console.error(err)
        }
    };

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <Controller
                name="schoolName"
                control={control}
                render={({ field }) => (
                    <TextField
                        {...field}
                        label="School Name"
                        variant="outlined"
                        margin="normal"
                        fullWidth
                        error={!!errors.schoolName}
                        helperText={errors.schoolName ? 'School Name is required' : ''}
                    />
                )}
                rules={{ required: true }}
            />
            <Controller
                name="ensembleName"
                control={control}
                render={({ field }) => (
                    <TextField
                        {...field}
                        label="Ensemble Name"
                        variant="outlined"
                        margin="normal"
                        fullWidth
                        error={!!errors.ensembleName}
                        helperText={errors.ensembleName ? 'Ensemble Name is required' : ''}
                    />
                )}
                rules={{ required: true }}
            />
            <Controller
                name="director"
                control={control}
                render={({ field }) => (
                    <TextField
                        {...field}
                        label="Director"
                        variant="outlined"
                        margin="normal"
                        fullWidth
                        error={!!errors.director}
                        helperText={errors.director ? 'Director is required' : ''}
                    />
                )}
                rules={{ required: true }}
            />
            <Box bgcolor="#f5f5f5" p={2} mb={2} borderRadius={4}>
                <Typography variant="body2" color="textSecondary" gutterBottom>
                    If you registered under the wrong classification, category or contest, please email <a href="mailto:<EMAIL>"><EMAIL></a>.
                </Typography>
            </Box>
            <Button 
                type="submit" 
                variant="contained" 
                fullWidth
                className='bg-iconGreen'
            >
                Submit
            </Button>
        </form>
    );
}

export default observer(EditEntryForm);
