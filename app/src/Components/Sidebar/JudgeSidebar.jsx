import { useContext, useState, useEffect } from "react";
import { observer } from "mobx-react";
import { AppProviderStore } from "./../../AppStore";
import { toJS } from "mobx";
import {
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  ListItemIcon,
  IconButton,
  Typography,
  Box,
  Divider,
} from "@mui/material";
import {
  CheckCircle as CheckCircleIcon,
  ArrowBack as ArrowBackIcon,
} from "@mui/icons-material";

const JudgeSidebar = ({ handleSelect, selectedGroup, assignments }) => {
  const { AppStore } = useContext(AppProviderStore);
  const [currentView, setCurrentView] = useState('categories');
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [selectedClassification, setSelectedClassification] = useState(null);

  // Update sidebar state when AppStore.selectedGroup changes
  useEffect(() => {
    // Only proceed if there's a selected group in AppStore
    if (AppStore.selectedGroup && AppStore.selectedGroup._id) {
      console.log('JudgeSidebar: AppStore.selectedGroup changed', AppStore.selectedGroup._id);

      // Find the category and classification for this group
      for (const assignment of assignments) {
        for (const classification of assignment.classifications) {
          const groupIndex = classification.groups.findIndex(g => g._id === AppStore.selectedGroup._id);
          if (groupIndex !== -1) {
            // Found the group in this classification
            console.log('JudgeSidebar: Found matching group in classification', classification.id.name);
            setSelectedCategory(assignment);
            setSelectedClassification(classification);
            AppStore.setCurrentSelectedCategory(assignment);
            AppStore.setCurrentSelectedClassification(classification);
            setCurrentView('groups');
            return; // Exit once we've found and set the state
          }
        }
      }
      console.log('JudgeSidebar: Could not find matching group in any classification');
    }
  }, [AppStore.selectedGroup, assignments]);

  // Add this function to handle group selection
  const handleGroupSelection = async (group, classificationId) => {
    if (AppStore.isRecording) {
      alert('Please stop recording before switching groups');
      return;
    }

    // Save current group's score before switching
    if (AppStore.selectedGroup && AppStore.judgeScore) {
      await AppStore.setGroupScore();
    }

    try {
      // Fetch the latest group data from the server to ensure we have the most up-to-date information
      const response = await fetch(`/api/groups/${group._id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch updated group data');
      }

      // Update the group with fresh data from the server
      const updatedGroup = await response.json();
      console.log('Fetched fresh group data:', updatedGroup);

      // Update the group in the groups array to ensure sidebar displays correct data
      const groupIndex = selectedClassification.groups.findIndex(g => g._id === updatedGroup._id);
      if (groupIndex !== -1) {
        selectedClassification.groups[groupIndex] = updatedGroup;
      }

      // Use the updated group data for the rest of the function
      group = updatedGroup;
    } catch (error) {
      console.error('Error fetching updated group data:', error);
      // Continue with existing group data if fetch fails
    }

    let judgeScore;
    let classificationIndex = -1;

    if (!group.classifications?.length) {
      judgeScore = group.judgeScores?.find(score => score.judgeId === AppStore.judge._id);
    } else {
      classificationIndex = group.classifications.findIndex(obj => obj.id === classificationId);
      if (classificationIndex !== -1 && group.classifications[classificationIndex]?.judgeScores) {
        judgeScore = group.classifications[classificationIndex].judgeScores.find(score => score.judgeId === AppStore.judge._id);
      }
    }

    // Set the selected classification index in AppStore
    AppStore.selectedClassificationIndex = classificationIndex;

    // Set default score if none exists
    const scoreToSet = judgeScore || {
      judgeId: AppStore.judge._id,
      score: null,
      movesOn: false,
      isCommended: false,
      isNational: false,
      isCitation: false,
      isState: false
    };

    // First set the group to ensure proper initialization
    AppStore.setGroup(group);

    // Call the handleSelect prop if it exists
    if (handleSelect && typeof handleSelect === 'function') {
      handleSelect(group);
    }

    // Then set the judge score
    AppStore.setJudgeScore(scoreToSet);
  };

  const getScore = (group, classificationId) => {
    let judgeScore;
    let inClassifications = -1;

    // Handle case where group has no classifications
    if (!group.classifications || !group.classifications.length) {
      if (group?.judgeScores && Array.isArray(group.judgeScores)) {
        judgeScore = group.judgeScores.find(score => score.judgeId === AppStore.judge._id);
      }
    } else {
      // Find the classification index
      let index = group.classifications.findIndex(obj => obj.id === classificationId);
      inClassifications = index;

      // Only proceed if we found a valid classification
      if (index !== -1 && group.classifications[index]) {
        // Make sure judgeScores is an array before trying to find a score
        if (group.classifications[index].judgeScores && Array.isArray(group.classifications[index].judgeScores)) {
          judgeScore = group.classifications[index].judgeScores.find(score => score.judgeId === AppStore.judge._id);
        }
      }
    }
    let defaultJudgeScore = {
      judgeId: AppStore.judge._id,
      score: null,
      movesOn: false,
      isCommended: false,
      isNational: false,
      isCitation: false,
      isState: false
    };
    if (inClassifications !== -1) {
      if (judgeScore) {
        judgeScore.classificationIndex = inClassifications;
      } else {
        defaultJudgeScore.classificationIndex = inClassifications;
      }
    }
    return judgeScore ? judgeScore : defaultJudgeScore;
  };

  const BackButton = ({ onClick, text }) => (
    <Box sx={{ p: 1 }}>
      <IconButton
        onClick={onClick}
        sx={{
          color: '#ffffff',
          padding: '4px',
          '&:hover': {
            color: '#3b8c6e',
          },
        }}
      >
        <ArrowBackIcon />
        <Typography
          variant="button"
          sx={{
            ml: 1,
            fontSize: '0.875rem'
          }}
        >
          {text}
        </Typography>
      </IconButton>
    </Box>
  );

  const renderCategories = () => (
    <List sx={{ width: '100%', p: 1 }}>
      {assignments.map((assignment) => (
        <ListItem
          key={assignment._id}
          disablePadding
          sx={{ mb: 1 }}
        >
          <ListItemButton
            onClick={() => {
              setSelectedCategory(assignment);
              AppStore.setCurrentSelectedCategory(assignment);
              setCurrentView('classifications');
            }}
            sx={{
              bgcolor: '#008544', // Changed from #3b8c6e
              borderRadius: 1,
              color: '#ffffff',
              '&:hover': {
                bgcolor: '#3b8c6e', // Changed from #008544
              },
            }}
          >
            <ListItemText primary={assignment.category.name} />
          </ListItemButton>
        </ListItem>
      ))}
    </List>
  );

  const renderClassifications = () => (
    <>
      <BackButton
        onClick={() => {
          setCurrentView('categories');
          setSelectedCategory(null);
          AppStore.setCurrentSelectedCategory(null);
        }}
        text="Back to Categories"
      />
      <Divider sx={{ bgcolor: 'rgba(255, 255, 255, 0.12)', my: 0.5 }} />
      <List sx={{ width: '100%', p: 1 }}>
        {selectedCategory.classifications
          .filter(classification => classification.groups && classification.groups.length > 0 || classification.phase !== undefined)
          .map((classification) => (
            <ListItem
              key={classification._id}
              disablePadding
              sx={{ mb: 1 }}
            >
              <ListItemButton
                onClick={() => {
                  setSelectedClassification(classification);
                  AppStore.setCurrentSelectedClassification(classification);
                  setCurrentView('groups');
                }}
                sx={{
                  bgcolor: '#008544', // Changed from #3b8c6e
                  borderRadius: 1,
                  color: '#ffffff',
                  '&:hover': {
                    bgcolor: '#3b8c6e', // Changed from #008544
                  },
                }}
              >
                <ListItemText primary={classification.id.name} />
              </ListItemButton>
            </ListItem>
          ))}
      </List>
    </>
  );

  const renderGroups = () => (
    <>
      <BackButton
        onClick={() => {
          if (AppStore.isRecording) {
            alert('Please stop recording before navigating away');
            return;
          }
          // Deselect the group in AppStore
          console.log('JudgeSidebar: Deselecting group when going back to classifications');
          AppStore.unselectGroup();

          // Update local state
          setCurrentView('classifications');
          setSelectedClassification(null);
          AppStore.setCurrentSelectedClassification(null);
        }}
        text="Back to Classifications"
      />
      <Divider sx={{ bgcolor: 'rgba(255, 255, 255, 0.12)', my: 0.5 }} />
      <List sx={{ width: '100%', p: 1 }}>
        {selectedClassification.groups.map((group) => {
          const score = getScore(group, selectedClassification.id._id);
          const isSelected = AppStore.selectedGroup?._id === group._id;

          // Create the list item text to display
          const maskedName = group.maskedName ||
            (group.classifications && group.classifications.find(
              (obj) => obj && (
                // Handle both cases: when obj.id is a string (ObjectId) or populated object
                (typeof obj.id === 'string' && obj.id === selectedClassification.id._id) ||
                (typeof obj.id === 'object' && obj.id._id === selectedClassification.id._id)
              )
            )?.maskedName) || '';
          const listItemText = `Group: ${maskedName}`;

          return (
            <ListItem
              key={group._id}
              disablePadding
              sx={{ mb: 1 }}
            >
              <ListItemButton
                selected={isSelected}
                onClick={() => handleGroupSelection(group, selectedClassification.id._id)}
                disabled={AppStore.isRecording}
                sx={{
                  bgcolor: isSelected ? '#3b8c6e' : '#ffffff',
                  color: isSelected ? '#ffffff' : '#000000',
                  borderRadius: 1,
                  '&:hover': {
                    bgcolor: '#3b8c6e',
                    color: '#ffffff',
                    // Add styles for disabled state
                    ...(AppStore.isRecording && {
                      bgcolor: '#ffffff',
                      cursor: 'not-allowed',
                      opacity: 0.5
                    })
                  },
                  '&.Mui-selected': {
                    bgcolor: '#3b8c6e',
                    '&:hover': {
                      bgcolor: '#3b8c6e',
                    },
                  },
                  // Add styles for disabled state
                  ...(AppStore.isRecording && {
                    opacity: 0.5,
                    cursor: 'not-allowed'
                  })
                }}
              >
                {score.score > 0 && (
                  <ListItemIcon sx={{ minWidth: 36 }}>
                    <CheckCircleIcon sx={{ color: isSelected ? '#ffffff' : '#3b8c6e' }} />
                  </ListItemIcon>
                )}
                <ListItemText
                  primary={listItemText}
                  secondary={score.score ? `Score: ${score.score}` : undefined}
                  secondaryTypographyProps={{
                    sx: { color: isSelected ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)' }
                  }}
                />
              </ListItemButton>
            </ListItem>
          );
        })}
      </List>
    </>
  );

  return (
    <Box
      sx={{
        width: 270,
        minHeight: '100%',
        bgcolor: '#0d3d75', // Changed back to previous blue
        pt: 2,
        overflow: 'auto',
      }}
    >
      {currentView === 'categories' && renderCategories()}
      {currentView === 'classifications' && renderClassifications()}
      {currentView === 'groups' && renderGroups()}
    </Box>
  );
};

export default observer(JudgeSidebar);
