import React, { useState, useEffect, useContext } from "react";
import { observer } from "mobx-react";
import {
    Dialog,
    DialogContent,
    DialogTitle,
    IconButton,
    Button,
    Snackbar,
    Alert,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
} from "@mui/material";
import { Close, AddCircle, Email } from "@mui/icons-material";
import { useForm } from "react-hook-form";
import Swal from 'sweetalert2';
import { AppProviderStore } from "../../AppStore";
import JudgeRow from './Components/JudgeRow';
import JudgeForm from './Components/JudgeForm';
import axios from 'axios';
import SendJudgeEmailModal from './Components/SendJudgeEmailModal';

const Judges = () => {
    const { control, handleSubmit, register, reset, watch, setValue } = useForm();
    const { AppStore } = useContext(AppProviderStore);
    const { addJudge, updateJudge, removeJudge, judges, categories } = AppStore;
    const [introDialogOpen, setIntroDialogOpen] = useState(false);
    const [formModalOpen, setFormModalOpen] = useState(false);
    const [isEdit, setIsEdit] = useState(false);
    const [judgeForms, setJudgeForms] = useState([{ id: 1 }]);
    const [currentJudge, setCurrentJudge] = useState(null);
    
    // Add state for Snackbar
    const [snackbar, setSnackbar] = useState({
        open: false,
        message: '',
        severity: 'success'
    });

    const handleCloseSnackbar = () => {
        setSnackbar({ ...snackbar, open: false });
    };

    const showNotification = (message, severity = 'success') => {
        setSnackbar({
            open: true,
            message,
            severity
        });
    };

    const handleEditJudge = (judge) => {
        setIsEdit(true);
        setJudgeForms([{ id: 1 }]);
        setCurrentJudge(judge);
        
        // Pre-populate the form with judge data
        setValue('name', judge.name);
        setValue('email', judge.email);
        
        // Pre-populate assigned categories and classifications
        if (judge.assigned && judge.assigned.length > 0) {
            setValue('assigned', judge.assigned, {
                shouldValidate: true,
                shouldDirty: true,
                shouldTouch: true
            });
        }
        
        setFormModalOpen(true);
    };

    const handleAddJudge = () => {
        setIsEdit(false);
        setJudgeForms([{ id: 1 }]);
        setCurrentJudge(null);
        // Reset all form fields
        reset({
            name: '',
            email: '',
            assigned: []
        });
        setFormModalOpen(true);
    };

    const handleCloseFormModal = () => {
        setFormModalOpen(false);
        setCurrentJudge(null);
        setIsEdit(false);
        // Reset all form fields
        reset({
            name: '',
            email: '',
            assigned: []
        });
    };

    const onSubmit = async (data) => {
        let success;
        if (!isEdit) {
            success = await addJudge(data);
            if (success) {
                showNotification('Judge added successfully');
            } else {
                showNotification('Failed to add judge', 'error');
            }
        } else {
            success = await updateJudge(currentJudge._id, data);
            if (success) {
                showNotification('Judge updated successfully');
            } else {
                showNotification('Failed to update judge', 'error');
            }
        }

        if (success) {
            handleCloseFormModal();
        }
    };

    const handleAddMore = () => {
        setJudgeForms((prevForms) => [
            ...prevForms,
            { id: prevForms.length > 0 ? prevForms[prevForms.length - 1].id + 1 : 1 },
        ]);
    };

    const handleRemove = (id) => {
        setJudgeForms((prevForms) => prevForms.filter((form) => form.id !== id));
    };

    const handleDeleteJudge = async (judgeId) => {
        const result = await Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, delete it!',
            customClass: {
                container: 'z-[1400]' // This ensures the modal appears above MUI components
            }
        });

        if (result.isConfirmed) {
            const success = await removeJudge(judgeId);
            if (success) {
                showNotification('Judge removed successfully');
                Swal.fire(
                    'Deleted!',
                    'The judge has been removed.',
                    'success'
                );
            } else {
                showNotification('Failed to remove judge', 'error');
                Swal.fire(
                    'Error!',
                    'Failed to remove the judge.',
                    'error'
                );
            }
        }
    };

    const [emailModalOpen, setEmailModalOpen] = useState(false);

    return (
        <div className="bg-white w-full">
            <div className="p-4 w-full grid grid-cols-[1.2fr_1.5fr_1fr_1fr_1fr_0.7fr] items-center gap-4 w-full">
                <div className="font-semibold text-black text-md">Name</div>
                <div className="font-semibold text-black text-md">Email</div>
                <div className="font-semibold text-black text-md">Judge Link</div>
                <div className="font-semibold text-black text-md">Intro</div>
                <div className="font-semibold text-black text-md">Assigned</div>
                <div className="font-semibold text-black text-md text-center">
                    Action
                </div>
            </div>

            <div className="flex flex-col gap-2">
                {judges?.map((item, index) => (
                    <JudgeRow
                        key={index}
                        item={item}
                        handleEditJudge={handleEditJudge}
                        removeJudge={handleDeleteJudge}
                        setIntroDialogOpen={setIntroDialogOpen}
                        introDialogOpen={introDialogOpen}
                    />
                ))}

                <button
                    type="button"
                    onClick={handleAddJudge}
                    className="flex items-center gap-2 px-4 py-3 bg-iconGreen text-white font-semibold rounded-md justify-center hover:bg-green-700 transition"
                >
                    <AddCircle className="w-[20px] h-[20px]" />
                    Add new Judge
                </button>
            </div>

            <Dialog
                open={formModalOpen}
                onClose={handleCloseFormModal}
                maxWidth="md"
                maxheight="80vh"
                fullWidth
                BackdropProps={{ style: { backgroundColor: "rgba(0, 0, 0, 0.1)" } }}
            >
                <DialogTitle className="text-white font-semibold bg-header px-4 py-3">
                    {isEdit ? "Edit Judge" : "Add Judge"}
                    <IconButton
                        aria-label="close"
                        onClick={handleCloseFormModal}
                        sx={{ position: "absolute", right: 8, top: 8 }}
                    >
                        <Close className="text-white" />
                    </IconButton>
                </DialogTitle>
                <DialogContent className="pt-4" sx={{ overflowY: 'auto' }}>
                    <JudgeForm
                        initialFields={judgeForms}
                        control={control}
                        register={register}
                        watch={watch}
                        setValue={setValue}
                        handleAddMore={handleAddMore}
                        handleRemove={handleRemove}
                        handleSubmit={handleSubmit(onSubmit)}
                        isEdit={isEdit}
                        categories={categories}
                        currentJudge={currentJudge}
                    />
                </DialogContent>
            </Dialog>

            <SendJudgeEmailModal 
                open={emailModalOpen}
                onClose={() => setEmailModalOpen(false)}
                onOpen={() => setEmailModalOpen(true)}
                showNotification={showNotification}
            />

            <Snackbar
                open={snackbar.open}
                autoHideDuration={6000}
                onClose={handleCloseSnackbar}
                anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
            >
                <Alert 
                    onClose={handleCloseSnackbar} 
                    severity={snackbar.severity}
                    sx={{ width: '100%' }}
                >
                    {snackbar.message}
                </Alert>
            </Snackbar>
        </div>
    );
};

export default observer(Judges);
