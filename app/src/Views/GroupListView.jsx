import axios from "axios";
import { useState, useEffect } from "react";
import { observer } from 'mobx-react';


function GroupListView({ handleSelect }) {
  //fetch groups
  //hard coded groups for testing workflow

  const groupList = [
    {
      name: "Group 1",
      trackList: [
        {
          title: "Vamo'alla flamenco",
          trackSrc: "track1.mp3",
          memos: { audioMemos: [], notes: [] },
        },
      ],
    },
    {
      name: "Group 2",
      trackList: [
        {
          title: "Land of Radiant Flowers",
          trackSrc: "track2.mp3",
          memos: { audioMemos: [], notes: [] },
        },
      ],
    },
  ];

  const renderGroups = groupList.map((group, index) => (
    <li key={index} onClick={() => handleSelect(group,'group')}>
      {group.name}
    </li>
  ));

  return (
    <>
      <h1>Event Name</h1>
      <ul>{renderGroups}</ul>
    </>
  );
}
export default observer(GroupListView);
