import React, { useState, useEffect, useRef } from 'react';
import ReactToPrint from 'react-to-print';
import { FaPrint } from 'react-icons/fa';

function ResultView({ contest, results }) {
    console.log('result view', contest)
    const [heading, setHeading] = useState(false);
    const [title, setTitle] = useState();

    const componentRef = useRef();

    useEffect(() => {
        let year = new Date().getFullYear();
        let newTitle;
        if (contest.title.includes('Honors')) {
            newTitle = contest.title;
            setHeading(`${year} Mark of Excellence`);
        } else {
            newTitle = `${year} ${contest.title}`;
            setHeading(false);
        }
        setTitle(newTitle);
    }, [contest.title]);

    function adjustFontSizeForPrint() {
        const ensembleElements = document.querySelectorAll('.flex-cell.ensembleName');
        const directorElements = document.querySelectorAll('.flex-cell.director');
        const locationElements = document.querySelectorAll('.flex-cell.location');
    
        ensembleElements.forEach(elem => {
            let fontSize;
            if (elem.textContent.length > 25) {
                fontSize = '0.95em';
            } else if (elem.textContent.length > 15) {
                fontSize = '0.98em';
            } else {
                fontSize = '1em';  // default size
            }
            elem.style.fontSize = fontSize;
    
            // Apply the same font size to corresponding director and location
            const parentRow = elem.closest('.flex-row');
            if (parentRow) {
                const directorElem = parentRow.querySelector('.flex-cell.director');
                const locationElem = parentRow.querySelector('.flex-cell.location');
                if (directorElem) directorElem.style.fontSize = fontSize;
                if (locationElem) locationElem.style.fontSize = fontSize;
            }
        });
    }

    function resetFontSizeAfterPrint() {
        const ensembleElements = document.querySelectorAll('.flex-cell.ensembleName');
        ensembleElements.forEach(elem => {
            elem.style.fontSize = '';  // reset to default size
        });
    }

    return (
        <div className="result-container">
            <ReactToPrint
                trigger={() => <button style={{ background: 'none', border: 'none', cursor: 'pointer' }}><FaPrint size={24} /></button>}
                content={() => componentRef.current}
                onBeforeGetContent={adjustFontSizeForPrint}
                onAfterPrint={resetFontSizeAfterPrint}
            />
            <div className="clearfix" ref={componentRef}>
                {heading && <h1 className='contest-title'>{heading}</h1>}
                <h1 className="contest-title">{title}</h1>

                {results && Object.keys(results).map((key) => {
                    const classificationResults = results[key];
                    if(key === 'State'){
                        key = 'State Level'
                    }
                    if (classificationResults.some(classificationData => classificationData.winners.length > 0)) {
                        return (
                            <div key={key} className="awardGrouping">
                                <h2 className='awardType'>{key} Winners</h2>
                                <br />
                                {classificationResults.map((classificationData, index) => {
                                    if (classificationData.winners.length > 0) {
                                        if (key !== 'State Level') {
                                            return (
                                                <div key={index}>
                                                    <h4 style={{ fontWeight: "bold" }}>{classificationData.classification}</h4>
                                                    {classificationData.winners.map((winner, wIndex) => (
                                                        <div className="flex-row" key={wIndex}>
                                                            <div className="flex-cell ensembleName">{winner.ensembleName}</div>
                                                            <div className="flex-cell director">{winner.director}</div>
                                                            <div className="flex-cell location">{winner.city}{winner.state ? `, ${winner.state}` : ''}</div>
                                                        </div>
                                                    ))}
                                                    <br />
                                                </div>
                                            );
                                        } else {
                                            // For 'state', render winners without extra wrapping divs or spacing
                                            return classificationData.winners.map((winner, wIndex) => (
                                                <div className="flex-row" key={wIndex}>
                                                    <div className="flex-cell ensembleName">{winner.ensembleName}</div>
                                                    <div className="flex-cell director">{winner.director}</div>
                                                    <div className="flex-cell location">{winner.city}{winner.state ? `, ${winner.state}` : ''}</div>
                                                </div>
                                            ));
                                        }
                                    }
                                    return null;
                                })}
                            </div>
                        );
                    }
                    return null;
                })}
            </div>
        </div>
    );
}

export default ResultView;
