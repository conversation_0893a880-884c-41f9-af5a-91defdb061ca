import { useForm, FormProvider } from "react-hook-form";
import { useState, useContext, useEffect } from "react";
import { AppProviderStore } from "../../AppStore";
import { observer } from "mobx-react";
import MultiStepForm from "./MultiStepForm";
import axios from 'axios';
import { Modal, CircularProgress, Typography, Box } from "@mui/material";
import Swal from "sweetalert2";
import { rulesContent } from "./rulesContent";
import { zodResolver } from "@hookform/resolvers/zod";
import { createFormSchema } from "./validationSchema";
import { toJS } from "mobx";

function RegistrationForm({ toggleRegistrationView }) {
  const { AppStore } = useContext(AppProviderStore);
  const { user, setUser } = AppStore;
  const [uploadProgress, setUploadProgress] = useState({});
  const [modalOpen, setModalOpen] = useState(false);
  const [hasSubmitted, setHasSubmitted] = useState(false);
  const [validationErrors, setValidationErrors] = useState([]);

  // Create form with Zod validation
  const methods = useForm({
    resolver: zodResolver(createFormSchema(1)), // We're only using one group in the multistep form
    mode: "all", // Validate on all events
    criteriaMode: "all", // Report all errors, not just the first one
    shouldFocusError: true // Focus on the first error field
  });

  useEffect(() => {
    if (hasSubmitted) {
      toggleRegistrationView()
    }
  }, [hasSubmitted, toggleRegistrationView]);

  function dirtyValues(dirtyFields, data) {
    if (dirtyFields === true || Array.isArray(dirtyFields)) {
      return data
    }
    return Object.fromEntries(
      Object.keys(dirtyFields).map((key) => [
        key,
        dirtyValues(dirtyFields[key], data[key])
      ])
    );

  }


  // Function to collect all validation errors
  const collectErrors = (errors) => {
    const errorList = [];

    // Recursive function to extract all error messages without field names
    const extractErrors = (obj, path = '') => {
      if (!obj) return;

      if (obj.message) {
        // Just push the error message without the field name
        errorList.push(obj.message);
      } else if (typeof obj === 'object') {
        Object.entries(obj).forEach(([key, value]) => {
          const newPath = path ? `${path}.${key}` : key;
          extractErrors(value, newPath);
        });
      }
    };

    extractErrors(errors);
    return errorList;
  };

  async function onSubmit(data) {
    try {
      // Check for validation errors
      if (Object.keys(methods.formState.errors).length > 0) {
        const allErrors = collectErrors(methods.formState.errors);
        setValidationErrors(allErrors);

        Swal.fire({
          title: "Validation Error",
          text: "Please fix all highlighted fields before submitting.",
          icon: "error",
        });

        return; // Stop submission if there are errors
      }

      // Clear any previous validation errors
      setValidationErrors([]);

      // Show the upload modal
      setModalOpen(true);

      console.log('Submitting data:', data);
      let groupData = dirtyValues(methods.formState.dirtyFields, data);
      let groupsArray = [];
      let success = true;

      for (const key in groupData) {
        let group = groupData[key];
        let groupInfo = { ...group };
        let emailGroup = { ...group };

        // Convert string values to numbers
        groupInfo.schoolEnrollment = Number(groupInfo.schoolEnrollment);
        groupInfo.registrationFee = Number(groupInfo.registrationFee || 0);
        let year = new Date().getFullYear();

        if (Array.isArray(groupInfo.classification)) {
          delete groupInfo.classification;
          groupInfo.classifications = [];
          for (const classification of group.classification) {
            groupInfo.classifications.push({
              id: classification,
            });
          }
        }

        delete groupInfo.directors;
        delete emailGroup.directors;

        if (group.directors && group.directors.length > 0) {
          const directors = group.directors.map((director) => {
            return `${director.name.prefix || ''} ${director.name.first} ${director.name.last}`;
          });

          groupInfo.director = directors.join(', ');
          emailGroup.director = directors.join(', ');
          groupInfo.email = user.email;
          groupInfo.userId = user._id;

          delete groupInfo.tracks;
          delete emailGroup.tracks;
          groupInfo.tracks = [];
          emailGroup.tracks = [];

          let tracks = group.tracks;
          console.log('Processing tracks:', tracks);

          for (const track of tracks) {
            // Process all tracks, even those without files
            let newTrack = { ...track };
            delete newTrack.file;
            emailGroup.tracks.push(newTrack);
            newTrack.memos = {
              audioMemos: [],
              notes: [],
              soloistNotes: []
            };

            // Only perform Minio upload if a file is present
            if (track.file !== undefined) {
              let path = `${year}/tracks/${user._id}/${groupInfo.ensembleName}`;
              if(AppStore.isDev){
                path = `/dev/${path}`
              }
              let fileName = `${path}/${track.file.name}`;
              let body = {
                filename: fileName
              };

              console.log('Getting presigned URL');
              let presignRes = await axios.post(`/api/minioPresignedUrl`, body);
              let presignedUrl = presignRes.data.presigned;

              let minioRes = await axios.put(presignedUrl, track.file, {
                onUploadProgress: (progressEvent) => {
                  const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                  setUploadProgress({ ...uploadProgress, [track.file.name]: percentCompleted });
                }
              });

              if (minioRes.status === 200) {
                setUploadProgress({ ...uploadProgress, [track.file.name]: 100 });
                newTrack.minioSrc = `https://app.foundationformusiceducation.org/fme-app/${fileName}`;
              }
            }

            // Save track information to database regardless of file presence
            let trackRes = await axios.post(`/api/tracks`, newTrack);
            let trackId = trackRes.data;
            groupInfo.tracks.push(trackId);
            groupInfo.judgesScores = [];
          }

          let groupRes = await axios.post(`/api/groups`, groupInfo);
          if (groupRes.status === 200) {
            console.log('Group saved:', groupRes.data);
            emailGroup._id = groupRes.data;
            groupInfo._id = groupRes.data;
            groupsArray.push(emailGroup);
          } else {
            console.error('Error saving group:', groupRes);
            success = false;
          }
        }
      }

      if (success) {
        let updatedUserRes = await axios.get(`/api/getUser/${user._id}`);
        setUser(updatedUserRes.data);
        setModalOpen(false);

        Swal.fire({
          title: "Success!",
          text: "You have successfully entered! We are excited to listen to your performances. Best of luck!",
          icon: "success",
          willClose: () => {
            setHasSubmitted(true);
          }
        });

        console.log('Sending confirmation email');
        let body = {
          groups: groupsArray,
          email: user.email
        };

        let emailRes = await axios.post(`/api/sendConfirmation`, body);
        console.log('Email confirmation response:', emailRes);
      } else {
        setModalOpen(false);

        Swal.fire({
          title: "Error!",
          text: "There was an error processing your entry. Please try again later.",
          icon: "error",
        });
      }
    } catch (error) {
      console.error("Form submission error:", error);
      setModalOpen(false);

      // Handle Zod validation errors
      if (error.errors) {
        // Use our error collection function for consistent formatting
        const allErrors = [];
        error.errors.forEach(err => {
          // Just push the error message without the field path
          allErrors.push(err.message);
        });
        setValidationErrors(allErrors);

        Swal.fire({
          title: "Validation Error",
          text: "Please check all highlighted fields and try again.",
          icon: "error",
        });
      } else {
        Swal.fire({
          title: "Error",
          text: "An unexpected error occurred. Please try again.",
          icon: "error",
        });
      }
    }
  }



  return (
    <FormProvider {...methods}>
      {/* {user.isAdmin && (<DevTool control={methods.control} placement="top-left" />)} */}
      <div className="rounded-2xl bg-white p-5 py-8">
        {/* Use the new MultiStepForm component */}
        <MultiStepForm
          groupNumber={0}
          onSubmit={methods.handleSubmit(onSubmit, errors => {
            // This function runs when there are validation errors
            const allErrors = collectErrors(errors);
            setValidationErrors(allErrors);

            // Show error message
            if (allErrors.length > 0) {
              Swal.fire({
                title: "Validation Error",
                text: "Please fix all highlighted fields before submitting.",
                icon: "error",
              });
            }
          })}
          validationErrors={validationErrors}
          rulesContent={rulesContent}
        />
      </div>

      <Modal open={modalOpen} onClose={() => setModalOpen(false)}>
        <div style={{ position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', background: 'white', padding: '40px', minWidth: '400px' }}>
          <Typography variant="h6" style={{ color: 'black' }}>Uploading...</Typography>
          {Object.keys(uploadProgress).map((file, index) => (
            <div key={`track_${index}`} style={{ marginTop: '20px' }}>
              <Typography variant="body1" style={{ color: 'black' }}>{file}</Typography>
              <Box sx={{ position: 'relative', display: 'inline-flex' }}>
                <CircularProgress variant="determinate" value={uploadProgress[file]} style={{ color: '#2e7d32' }} />
                <Box
                  sx={{
                    top: 0,
                    left: 0,
                    bottom: 0,
                    right: 0,
                    position: 'absolute',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <Typography variant="caption" component="div" color="text.primary">
                    {`${Math.round(uploadProgress[file])}%`}
                  </Typography>
                </Box>
              </Box>
            </div>
          ))}
        </div>
      </Modal>
    </FormProvider>
  );
}

export default observer(RegistrationForm);
