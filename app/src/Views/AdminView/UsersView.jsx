import React, { useState, useEffect, useContext } from "react";
import { Tab, Tabs, Typography, Box } from "@mui/material";
import { AppProviderStore } from "../../AppStore";
import { observer } from "mobx-react";
import { toJS } from "mobx";
import UsersTable from "../../Components/AdminViewComponents/UsersTable"

function UsersView() {
  const { AppStore } = useContext(AppProviderStore);
  const { users, getUsers } = AppStore



  return (
    <div className="w-full">
      <UsersTable />
    </div>
  );
}

export default observer(UsersView);



