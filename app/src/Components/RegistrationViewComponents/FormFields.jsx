import { observer } from "mobx-react";
import { AppProviderStore } from "../../AppStore";
import { useFormContext, Controller } from "react-hook-form";
import { useContext, useEffect, useState } from "react";
import { toJS } from "mobx";
import {
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Box,
  Button,
  Checkbox,
  IconButton,
  Tooltip,
  FormLabel,
  InputAdornment,
  FormHelperText,
  Alert
} from "@mui/material";
import CancelIcon from "@mui/icons-material/Cancel";
import AddIcon from "@mui/icons-material/Add";
import AttachFileIcon from "@mui/icons-material/AttachFile";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { MuiFileInput } from "mui-file-input";

function FormFields({ groupNumber }) {
  //console.log('rendering form fields')
  const { AppStore } = useContext(AppProviderStore);
  const { formOptions } = AppStore;
  const {
    register,
    unregister,
    watch,
    control,
    getValues,
    setValue,
    formState: { errors, isValid, dirtyFields },
  } = useFormContext();
  const [isShippingDifferent, setShippingDifferent] = useState(false);
  const states = [
    "AK", "AL", "AR", "AZ", "CA", "CO", "CT", "DE", "FL", "GA",
    "IA", "HI", "ID", "IL", "IN", "KS", "KY", "LA", "MA", "MD",
    "ME", "MI", "MN", "MO", "MS", "MT", "NC", "ND", "NE", "NH",
    "NJ", "NM", "NV", "NY", "OH", "OK", "OR", "PA", "RI", "SC",
    "SD", "TN", "TX", "UT", "VA", "VT", "WA", "WI", "WV", "WY",
  ];

  const [directors, setDirectors] = useState([{ id: 0 }]); // Array to track directors
  const [soloists, setSoloists] = useState(Array.from({ length: 3 }, () => []));
  const [selectionFields, setSelectionFields] = useState([])
  const [numberOfSelections, setNumberOfSelections] = useState()
  // const [isRequired, setIsRequired] = useState()
  // const [isOptional,setIsOptional] = useState()
  // const [hasSoloist,setHasSoloist] = useState()

  const selectedContest = watch(`group_${groupNumber}.contest`, "");
  const selectedCategory = watch(`group_${groupNumber}.category`, "");
  const selectedClassification = watch(`group_${groupNumber}.classification`, "");
  const watcher = watch()
  // useEffect(()=>{
  //   if(selectionFields.length){
  //     for(let i = 0; i < numberOfSelections; i++){
  //       unregister([
  //         `group_${groupNumber}.tracks.${i}.title`,
  //         `group_${groupNumber}.tracks.${i}.composer`,
  //         `group_${groupNumber}.tracks.${i}.duration`,
  //         `group_${groupNumber}.tracks.${i}.dateRecorded`,
  //         `group_${groupNumber}.tracks.${i}.venue`,
  //         `group_${groupNumber}.tracks.${i}.file`,
  //         ,
  //       ]);
  //     }
  //     setNumberOfSelections()
  //     setIsOptional()
  //     setHasSoloist()
  //   }
  //   const generateSelectionFields = () =>{
  //     let choralId = "64345656dd98890aad8cef2d";
  //     let jazzId = "643456dbdd98890aad8cef2e";
  //     let percussionId = "643456dbdd98890aad8cef30";

  //     if (selectedCategory !== choralId && selectedCategory !== percussionId) {
  //       setHasSoloist(true)
  //     }else{
  //       setHasSoloist(false)
  //     }


  //     if (selectedCategory === choralId) {
  //       setNumberOfSelections(3)
  //       setIsOptional(false)
  //     } else if (
  //       selectedCategory === jazzId ||
  //       selectedCategory === percussionId
  //     ) {
  //       setNumberOfSelections(3);
  //       setIsOptional(true);
  //     }else{
  //       setNumberOfSelections(2)
  //       setIsOptional(true)
  //     }

  //     const musicFields = [];
  //     console.log('setting selections: ')
  //     console.log('Selection size: ', numberOfSelections, 'hasSoloist', hasSoloist, 'isOptional', isOptional)
  //     for (let i = 0; i < numberOfSelections; i++) {
  //       let formlabel = `Selection #${i + 1}`;
  //       let isRequired = true
  //       if (i === 1) {
  //         formlabel += ` (If you are a National Winner this will be included in the compilation )`;
  //       }
  //       if (isOptional && i === 2) {
  //         formlabel += ` (Optional)`;
  //         isRequired = !isOptional
  //       }
  //       musicFields.push(
  //         <div key={`selection-${i}`} className="flex flex-col">
  //           <FormLabel component="legend">{formlabel}</FormLabel>
  //           <div className="grid-cols-[1fr,1fr] grid gap-4">
  //             <TextField
  //               {...register(`group_${groupNumber}.tracks.${i}.title`)}
  //               required={isRequired}
  //               label="Title"
  //               className="rounded border"
  //             />

  //             <TextField
  //               {...register(`group_${groupNumber}.tracks.${i}.composer`)}
  //               required={isRequired}
  //               label="Composer/Arranger"
  //               className="rounded border"
  //             />

  //             <Controller
  //               name={`group_${groupNumber}.tracks.${i}.duration`}
  //               required={isRequired}
  //               control={control}
  //               defaultValue=""
  //               render={({ field }) => (
  //                 <DurationInput value={field.value} onChange={field.onChange} />
  //               )}
  //             />

  //             <LocalizationProvider dateAdapter={AdapterDayjs}>
  //               <Controller
  //                 control={control}
  //                 name={`group_${groupNumber}.tracks.${i}.dateRecorded`}
  //                 render={({ field }) => (
  //                   <DatePicker
  //                     label="Date of Recording"
  //                     value={field.value || null}
  //                     onChange={(date) => field.onChange(date)}
  //                     TextFieldComponent={TextField} // Use TextField component directly
  //                   />
  //                 )}
  //               />
  //             </LocalizationProvider>

  //             <TextField
  //               {...register(`group_${groupNumber}.tracks.${i}.venue`)}
  //               required={isRequired}
  //               label="Recording Event/Venue"
  //               className="rounded border"
  //             />
  //             <Controller
  //               name={`group_${groupNumber}.tracks.${i}.file`}
  //               control={control}
  //               render={({ field, fieldState }) => (
  //                 <MuiFileInput
  //                   required={isRequired}
  //                   {...field}
  //                   InputProps={{
  //                     inputProps: {
  //                       accept: ".wav, .mp3",
  //                     },
  //                     endAdornment: <AttachFileIcon />,
  //                   }}
  //                   label="Wav File Upload"
  //                   helperText={fieldState.invalid ? "File is invalid" : ""}
  //                   error={fieldState.invalid}
  //                 />
  //               )}
  //             />
  //           </div>
  //           {selectedClassification && hasSoloist && (
  //             <div className="my-4 flex flex-col">
  //               {soloistSection(i)}
  //               <Button
  //                 variant="contained"
  //                 color="success"
  //                 startIcon={<AddIcon fontSize="large" />}
  //                 onClick={() => addSoloist(i)}
  //                 className="w-fit px-5 py-3"
  //               >
  //                 Add Soloist
  //               </Button>
  //             </div>
  //           )}

  //           <div className="my-4 flex flex-col">

  //           </div>
  //         </div>
  //       );
  //     }
  //     setSelectionFields(musicFields)
  //   }
  //   generateSelectionFields()
  // },[selectedCategory])

  useEffect(() => {
    // console.log(toJS(formOptions), selectedContest)
    // console.log('selectedClassification')
    // console.log(selectedClassification)
    let price = 0
    for (const key in formOptions) {
      if (formOptions[key]._id === selectedContest) {
        price = formOptions[key].price
      }
    }
    if (selectedContest && !selectedClassification) {
      //console.log('no classification')
    }
    if (Array.isArray(selectedClassification) && selectedClassification.length === 2) {
      price = getValues(`group_${groupNumber}.registrationFee`) * 2
    }
    setValue(`group_${groupNumber}.registrationFee`, price, { shouldDirty: true })
  }, [selectedContest, selectedClassification])

  const addDirector = () => {
    const newDirectorId = directors.length; // Generate a unique id for the new director
    setDirectors([...directors, { id: newDirectorId }]); // Add a new director
  };

  const removeDirector = (indexToRemove) => {
    setDirectors((prevDirectors) =>
      prevDirectors.filter((_, index) => index !== indexToRemove)
    );
  };

  const addSoloist = (trackId) => {
    setSoloists((prevSoloists) => {
      const newSoloistIndex = prevSoloists[trackId].length;
      const updatedSoloists = [...prevSoloists];
      updatedSoloists[trackId] = [...updatedSoloists[trackId], { id: newSoloistIndex }];
      return updatedSoloists;
    });
  };

  const removeSoloist = (trackId, soloistIndex) => {
    //console.log('removing soloist?', trackId, soloistIndex)
    setSoloists((prevSoloists) => {
      const updatedSoloists = [...prevSoloists];
      updatedSoloists[trackId] = prevSoloists[trackId].filter((_, index) => index !== soloistIndex);
      return updatedSoloists;
    });
  };

  const setDisabled = (classificationId) => {
    let isDisabled = false;
    const aClasses = ["643453f8dd98890aad8cef20", "643453f8dd98890aad8cef21", "643453f8dd98890aad8cef22", "643453f8dd98890aad8cef23", "643453f8dd98890aad8cef24"];
    const newClasses = ["643453f8dd98890aad8cef25", "643453f8dd98890aad8cef26"];
    if (!Array.isArray(selectedClassification)) {
      return false
    }
    if (selectedClassification.length > 1) {
      if (selectedClassification.includes(classificationId)) {
        return false
      } else return true
    }
    const selected = selectedClassification[0];
    if (!selectedClassification || selected === classificationId) {
      return isDisabled;
    } else {


      if (aClasses.includes(classificationId)) {
        isDisabled = aClasses.includes(selected);
      } else {
        isDisabled = newClasses.includes(selected);
      }
    }
    return isDisabled;
  };



  const soloistSection = (trackIndex) => {
    return (
      <>
        <br />
        {soloists[trackIndex].map(({ id }, index) => (
          <div key={id} className="flex items-center gap-4 relative" style={{ marginBottom: '8px' }}>
            <Tooltip title="Remove Soloist" placement="left">
              <IconButton
                aria-label="Remove Soloist"
                className="absolute left-[-30px]"
                onClick={() => {
                  for (let i = index; i < soloists[trackIndex].length; i++) {
                    unregister([
                      `group_${groupNumber}.tracks.${trackIndex}.soloists.${i}.name`,
                      `group_${groupNumber}.tracks.${trackIndex}.soloists.${i}.instrument`,
                    ]);
                  }
                  removeSoloist(trackIndex, index);
                }}
              >
                <CancelIcon color="error" />
              </IconButton>
            </Tooltip>
            <div className="flex items-center gap-4" style={{ flex: 1 }}>
              <TextField
                {...register(
                  `group_${groupNumber}.tracks.${trackIndex}.soloists.${index}.name`
                  , { required: true })}
                // required
                label="Soloist Name"
                className="rounded border"
                style={{ width: '100%' }}
              />
              <TextField
                {...register(
                  `group_${groupNumber}.tracks.${trackIndex}.soloists.${index}.instrument`
                  , { required: true })}
                // required
                label="Instrument"
                className="rounded border"
                style={{ width: '100%' }}
              />
                            <TextField
                {...register(
                  `group_${groupNumber}.tracks.${trackIndex}.soloists.${index}.timeStamp`
            )}
                // required
                label="Time Stamp"
                className="rounded border"
                style={{ width: '100%' }}
              />
            </div>
          </div>
        ))}
        <br />
      </>
    );
  };


  //looking into issue with duration input re-rendering
  const DurationInput = ({ value, onChange }) => {
    const handleChange = (e) => {
      const input = e.target.value;
      // Format input to mm:ss
      const formattedInput = input.replace(/[^\d:]/g, "").slice(0, 5); // Allow only digits and ':' and limit to 5 characters
      onChange(formattedInput);
    };

    return (
      <TextField
        value={value}
        onChange={handleChange}
        label="Duration (mm:ss)"
        className="rounded border"
      />
    );
  };


  const musicSelections = () => {
    //console.log('rendering')
    let numberOfSelections = 2;
    let isOptional = false;
    let choralId = "64345656dd98890aad8cef2d";
    let jazzId = "643456dbdd98890aad8cef2e";
    let percussionId = "643456dbdd98890aad8cef30";
    let citationId = "64345c8fdd98890aad8cef3d"
    let hasSoloist = false

    if(selectedContest !== citationId){
      if (selectedCategory !== choralId && selectedCategory !== percussionId) {
        hasSoloist = true
      }
    }



    if (selectedCategory === choralId) {
      numberOfSelections = 3;
    } else if (
      selectedCategory === jazzId ||
      selectedCategory === percussionId
    ) {
      numberOfSelections = 3;
      isOptional = true;
    }

    const musicFields = [];

    for (let i = 0; i < numberOfSelections; i++) {
      let formlabel = `Selection #${i + 1}`;
      let isRequired = false;
      if (i === 1) {
        formlabel += ` (If you are a National Winner this will be included in the compilation )`;
      }
      if (isOptional && i === 2) {
        formlabel += ` (Optional)`;
        isRequired = false
      }
      musicFields.push(
        <div key={`selection-${i}`} className="flex flex-col">
          <FormLabel component="legend" sx={{ fontWeight: 'bold' }}>{formlabel}</FormLabel>
          <div className="grid-cols-[1fr,1fr] grid gap-4">
            <TextField
              {...register(`group_${groupNumber}.tracks.${i}.title`, { required: true })}
              required
              label="Title"
              className="rounded border"
              error={!!errors?.[`group_${groupNumber}`]?.tracks?.[i]?.title}
              helperText={errors?.[`group_${groupNumber}`]?.tracks?.[i]?.title?.message}
            />

            <TextField
              {...register(`group_${groupNumber}.tracks.${i}.composer`, { required: true })}
              required
              label="Composer/Arranger"
              className="rounded border"
              error={!!errors?.[`group_${groupNumber}`]?.tracks?.[i]?.composer}
              helperText={errors?.[`group_${groupNumber}`]?.tracks?.[i]?.composer?.message}
            />
            <TextField
              {...register(`group_${groupNumber}.tracks.${i}.duration`, { required: true })}
              required
              label="Duration (mm:ss)"
              className="rounded border"
              error={!!errors?.[`group_${groupNumber}`]?.tracks?.[i]?.duration}
              helperText={errors?.[`group_${groupNumber}`]?.tracks?.[i]?.duration?.message}
            />

            {/* <Controller
              name={`group_${groupNumber}.tracks.${i}.duration`}
              // required={isRequired}
              control={control}
              defaultValue=""
              render={({ field }) => (
                <DurationInput value={field.value} onChange={field.onChange} />
              )}
            /> */}

            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <Controller
                control={control}
                name={`group_${groupNumber}.tracks.${i}.dateRecorded`}
                render={({ field, fieldState }) => (
                  <DatePicker
                    label="Date of Recording"
                    value={field.value || null}
                    onChange={(date) => field.onChange(date)}
                    TextFieldComponent={(params) => (
                      <TextField
                        {...params}
                        error={!!fieldState.error}
                        helperText={fieldState.error?.message}
                      />
                    )}
                  />
                )}
              />
            </LocalizationProvider>

            <TextField
              {...register(`group_${groupNumber}.tracks.${i}.venue`, { required: true })}
              required
              label="Recording Event/Venue"
              className="rounded border"
              error={!!errors?.[`group_${groupNumber}`]?.tracks?.[i]?.venue}
              helperText={errors?.[`group_${groupNumber}`]?.tracks?.[i]?.venue?.message}
            />
            <Controller
              name={`group_${groupNumber}.tracks.${i}.file`}
              control={control}
              rules={{ required: "Audio file is required" }}
              render={({ field, fieldState }) => (
                <MuiFileInput
                  required
                  {...field}
                  InputProps={{
                    inputProps: {
                      accept: ".wav, .mp3",
                    },
                    endAdornment: <AttachFileIcon />,
                  }}
                  label="Wav File Upload"
                  helperText={fieldState.error ? fieldState.error.message : ""}
                  error={!!fieldState.error}
                />
              )}
            />
          </div>
          {selectedClassification && hasSoloist && (
            <div className="my-4 flex flex-col">
              {soloistSection(i)}
              <Button
                variant="contained"
                color="success"
                startIcon={<AddIcon fontSize="large" />}
                onClick={() => addSoloist(i)}
                className="w-fit px-5 py-3"
              >
                Add Soloist
              </Button>
              <span>*Add a time stamp to help identify multiple soloist of the same instrument</span>

            </div>
          )}
          <div className="my-4 flex flex-col">

          </div>
        </div>
      );
    }
    return musicFields;
  };

  return (
    <div className="flex flex-col gap-5">
      <label>Director(s) Name (for Publication)</label>
      {directors.map(({ id }, index) => (
        <div
          key={id}
          className="grid gap-5 grid-cols-[0.5fr,1fr,1fr] items-center"
        >
          <div className="flex flex-col relative">
            {index !== 0 && (
              <Tooltip
                title="Delete Director"
                className="absolute left-[-22px] top-[20px]"
              >
                <CancelIcon
                  className=""
                  color="error"
                  onClick={() => {
                    for (let i = index; i < directors.length; i++) {
                      unregister([
                        `group_${groupNumber}.directors.${i}.name.prefix`,
                        `group_${groupNumber}.directors.${i}.name.first`,
                        `group_${groupNumber}.directors.${i}.name.last`,
                      ]);
                    }
                    removeDirector(index);
                  }}
                />
              </Tooltip>
            )}
            <Controller
              name={`group_${groupNumber}.directors.${index}.name.prefix`}
              defaultValue={""}
              control={control}
              rules={{ required: true }}
              render={({ field: { value, onChange } }) => (
                <FormControl>
                  <InputLabel required id={`directors_${index}.prefix-label`}>
                    Prefix
                  </InputLabel>
                  <Select
                    required
                    labelId={`directors_${index}.prefix-label`}
                    id={`${groupNumber}.directors.${index}.name.prefix-select`}
                    value={value}
                    autoWidth
                    label="Age"
                    className="w-full"
                    onChange={onChange}
                  >
                    <MenuItem value={"Dr."}>Dr.</MenuItem>
                    <MenuItem value={"Mr."}>Mr.</MenuItem>
                    <MenuItem value={"Ms."}>Ms.</MenuItem>
                    <MenuItem value={"Mrs."}>Mrs.</MenuItem>
                    <MenuItem value={"Mx."}>Mx.</MenuItem>
                  </Select>
                </FormControl>
              )}
            />
            {errors[`group_${groupNumber}.directors.${index}.name.prefix`] && (
              <span className="text-red absolute left-[0px] bottom-[-17px] text-xs">
                * This field is required
              </span>
            )}
          </div>
          <div className="flex flex-col relative">
            <TextField
              {...register(
                `group_${groupNumber}.directors.${index}.name.first`,
                {
                  required: true,
                }
              )}
              required
              label="First Name"
              className="rounded border"
            />
            {errors[`group_${groupNumber}.directors.${index}.name.first`] && (
              <span className="text-red absolute left-[0px] bottom-[-17px] text-xs">
                This field is required
              </span>
            )}
          </div>
          <div className="flex flex-col relative">
            <TextField
              {...register(
                `group_${groupNumber}.directors.${index}.name.last`,
                {
                  required: true,
                }
              )}
              required
              label="Last Name"
              className="rounded border"
            />
            {errors[`group_${groupNumber}.directors.${index}.name.last`] && (
              <span className="text-red absolute left-[0px] bottom-[-17px] text-xs">
                This field is required
              </span>
            )}
          </div>
        </div>
      ))}
      <Button
        variant="contained"
        color="success"
        startIcon={<AddIcon fontSize="large" />}
        onClick={addDirector}
        className="w-fit px-5 py-3"
      >
        Add A Director
      </Button>
      <label>
        <strong>Ensemble Name - Do not abbreviate. Exactly as you wish for it in
        publication. ie: Lamar High School Wind Ensemble.</strong>
      </label>
      <div className="grid grid-cols-[1fr,1fr] gap-4">
        <TextField
          {...register(`group_${groupNumber}.ensembleName`, { required: true })}
          required
          label="Ensemble Name"
          className="rounded border"
          error={!!errors?.[`group_${groupNumber}`]?.ensembleName}
          helperText={errors?.[`group_${groupNumber}`]?.ensembleName?.message}
        />

        <TextField
          {...register(`group_${groupNumber}.schoolName`, { required: true })}
          required
          label="School Name"
          className="rounded border"
          error={!!errors?.[`group_${groupNumber}`]?.schoolName}
          helperText={errors?.[`group_${groupNumber}`]?.schoolName?.message}
        />
        <TextField
          {...register(`group_${groupNumber}.schoolEnrollment`, { required: true })}
          required
          label="School Enrollment Size"
          className="rounded border"
          type="number"
          error={!!errors?.[`group_${groupNumber}`]?.schoolEnrollment}
          helperText={errors?.[`group_${groupNumber}`]?.schoolEnrollment?.message}
        />
      </div>
      <div className="grid grid-cols-[1fr,1fr] gap-4">
        <FormControl error={!!errors?.[`group_${groupNumber}`]?.contest}>
          <InputLabel required>Select Contest</InputLabel>
          <Select
            {...register(`group_${groupNumber}.contest`, { required: true })}
            required
            defaultValue=""
            label="Select Contest"
          >
            <MenuItem value="" disabled>
              Select Contest
            </MenuItem>
            {Object.keys(formOptions).map((key) => (
              <MenuItem key={formOptions[key]._id} value={formOptions[key]._id}>
                {formOptions[key].name}
              </MenuItem>
            ))}
          </Select>
          {errors?.[`group_${groupNumber}`]?.contest && (
            <FormHelperText>{errors[`group_${groupNumber}`].contest.message}</FormHelperText>
          )}
        </FormControl>

        <FormControl error={!!errors?.[`group_${groupNumber}`]?.category}>
          <InputLabel required id="">Select Category</InputLabel>
          <Select
            {...register(`group_${groupNumber}.category`, { required: true })}
            required
            defaultValue=""
            label="Select Category"
          >
            <MenuItem value="" disabled>
              Select Category
            </MenuItem>
            {selectedContest &&
              formOptions
                .find((option) => option._id === selectedContest)
                ?.categories.map((category) => (
                  <MenuItem key={category._id} value={category._id}>
                    {category.name}
                  </MenuItem>
                ))}
          </Select>
          {errors?.[`group_${groupNumber}`]?.category && (
            <FormHelperText>{errors[`group_${groupNumber}`].category.message}</FormHelperText>
          )}
        </FormControl>
        {selectedCategory === "643456dbdd98890aad8cef31" ? ( //if national wind band honors 643456dbdd98890aad8cef31
          <Controller
            name={`group_${groupNumber}.classification`}
            control={control}
            rules={{ required: "Classification is required" }}
            defaultValue={[""]}
            render={({ field: { value, onChange }, fieldState: { error } }) => (
              <FormControl error={!!error}>
                <InputLabel>Select Classification</InputLabel>
                <Select
                  labelId="select-classification-label"
                  id={`select-classification-${groupNumber}`}
                  label="Select Classification"
                  multiple
                  value={Array.isArray(value) ? value : []}
                  onChange={(event) => {
                    const selectedValues = event.target.value;
                    if (selectedValues.length <= 2) {
                      onChange(selectedValues);
                    }
                  }}
                  renderValue={(selected) => (
                    <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
                      {selected.map((value) => (
                        <Chip
                          key={value}
                          label={
                            formOptions
                              .find((option) => option._id === selectedContest)
                              ?.categories.find(
                                (category) => category._id === selectedCategory
                              )
                              ?.classifications.find(
                                (classification) => classification._id === value
                              )?.name || ""
                          }
                        />
                      ))}

                    </Box>
                  )}
                >
                  <MenuItem value="" disabled>
                    Select up to two Classification
                  </MenuItem>
                  {selectedCategory &&
                    formOptions
                      .find((option) => option._id === selectedContest)
                      ?.categories.find(
                        (category) => category._id === selectedCategory
                      )
                      ?.classifications.map((classification) => (
                        <MenuItem
                          key={classification._id}
                          value={classification._id}
                          disabled={setDisabled(classification._id)}
                        >
                          {classification.name}
                        </MenuItem>
                      ))}
                </Select>
                {error ? (
                  <FormHelperText error>{error.message}</FormHelperText>
                ) : (
                  <FormHelperText>Can Select up to 2 (ex: A Class + New Music Class)</FormHelperText>
                )}
              </FormControl>
            )}
          />
        ) : (
          <FormControl error={!!errors?.[`group_${groupNumber}`]?.classification}>
            <InputLabel required>Select Classification</InputLabel>
            <Select
              {...register(`group_${groupNumber}.classification`, { required: true })}
              required
              defaultValue=""
              label="Select Classification"
            >
              <MenuItem value="" disabled>
                Select Classification
              </MenuItem>
              {selectedCategory &&
                formOptions
                  .find((option) => option._id === selectedContest)
                  ?.categories.find(
                    (category) => category._id === selectedCategory
                  )
                  ?.classifications.map((classification) => (
                    <MenuItem
                      key={classification._id}
                      value={classification._id}
                    >
                      {classification.name}
                    </MenuItem>
                  ))}
            </Select>
            {errors?.[`group_${groupNumber}`]?.classification && (
              <FormHelperText error>{errors[`group_${groupNumber}`].classification.message}</FormHelperText>
            )}
          </FormControl>
        )}
        <TextField
          {...register(`group_${groupNumber}.registrationFee`)}
          label="Registration Fee"
          aria-readonly
          disabled
          InputProps={{
            startAdornment: <InputAdornment position="start">$</InputAdornment>,
          }}
        />
      </div>
      <div className="flex flex-col relative">
        <TextField
          {...register(`group_${groupNumber}.cellphone`, { required: true })}
          required
          label="Cell Phone"
          className="rounded border"
          type="tel"
          error={!!errors?.[`group_${groupNumber}`]?.cellphone}
          helperText={errors?.[`group_${groupNumber}`]?.cellphone?.message}
        />
      </div>

      <label>Mailing Address:</label>
      <div className="grid grid-cols-[1fr,1fr] grid-rows-[1fr,1fr] gap-4">
        <TextField
          {...register(`group_${groupNumber}.mailingAddress.street`, { required: true })}
          required
          label="Street"
          className="rounded border"
          error={!!errors?.[`group_${groupNumber}`]?.mailingAddress?.street}
          helperText={errors?.[`group_${groupNumber}`]?.mailingAddress?.street?.message}
        />
        <TextField
          {...register(`group_${groupNumber}.mailingAddress.city`, { required: true })}
          required
          label="City"
          className="rounded border"
          error={!!errors?.[`group_${groupNumber}`]?.mailingAddress?.city}
          helperText={errors?.[`group_${groupNumber}`]?.mailingAddress?.city?.message}
        />
        <FormControl error={!!errors?.[`group_${groupNumber}`]?.mailingAddress?.state}>
          <InputLabel required>State</InputLabel>
          <Select
            {...register(`group_${groupNumber}.mailingAddress.state`, { required: true })}
            className="rounded"
            defaultValue=""
            label="State"
          >
            {states.map((state) => (
              <MenuItem key={state} value={state}>
                {state}
              </MenuItem>
            ))}
          </Select>
          {errors?.[`group_${groupNumber}`]?.mailingAddress?.state && (
            <FormHelperText>{errors[`group_${groupNumber}`].mailingAddress.state.message}</FormHelperText>
          )}
        </FormControl>
        <TextField
          {...register(`group_${groupNumber}.mailingAddress.zip`, { required: true })}
          required
          label="ZIP"
          className="rounded border"
          error={!!errors?.[`group_${groupNumber}`]?.mailingAddress?.zip}
          helperText={errors?.[`group_${groupNumber}`]?.mailingAddress?.zip?.message}
        />
      </div>

      <label>
        <Checkbox onChange={() => setShippingDifferent(!isShippingDifferent)} />
        Shipping address is different than mailing?
      </label>

      {isShippingDifferent && (
        <>
          <label>Shipping Address:</label>
          <div className="grid grid-cols-[1fr,1fr] grid-rows-[1fr,1fr] gap-4">
            <TextField
              {...register(`group_${groupNumber}.shippingAddress.street`, { required: true })}
              required
              label="Shipping Street"
              className="rounded border"
              error={!!errors?.[`group_${groupNumber}`]?.shippingAddress?.street}
              helperText={errors?.[`group_${groupNumber}`]?.shippingAddress?.street?.message}
            />
            <TextField
              {...register(`group_${groupNumber}.shippingAddress.city`, { required: true })}
              required
              label="Shipping City"
              className="rounded border"
              error={!!errors?.[`group_${groupNumber}`]?.shippingAddress?.city}
              helperText={errors?.[`group_${groupNumber}`]?.shippingAddress?.city?.message}
            />
            <FormControl error={!!errors?.[`group_${groupNumber}`]?.shippingAddress?.state}>
              <InputLabel required>State</InputLabel>
                <Select
                  {...register(`group_${groupNumber}.shippingAddress.state`, { required: true })}
                  required
                  className="rounded"
                  label="State"
                  defaultValue=""
                >
                  {states.map((state) => (
                    <MenuItem key={state} value={state}>
                      {state}
                    </MenuItem>
                  ))}
                </Select>
                {errors?.[`group_${groupNumber}`]?.shippingAddress?.state && (
                  <FormHelperText>{errors[`group_${groupNumber}`].shippingAddress.state.message}</FormHelperText>
                )}
            </FormControl>
            <TextField
              {...register(`group_${groupNumber}.shippingAddress.zip`, { required: true })}
              required
              label="Shipping ZIP"
              className="rounded border"
              error={!!errors?.[`group_${groupNumber}`]?.shippingAddress?.zip}
              helperText={errors?.[`group_${groupNumber}`]?.shippingAddress?.zip?.message}
            />
          </div>
        </>
      )}
      {selectedClassification && (
        <>
          <FormLabel component="legend">Music Selection Information</FormLabel>
          {musicSelections()}
        </>
      )}

    </div>
  );
}

export default observer(FormFields);