import { useState, useEffect, use<PERSON>allback, useContext } from "react";
import { observer } from "mobx-react";
import {
  DataGrid,
  GridToolbarContainer,
  GridToolbarColumnsButton,
  GridToolbarFilterButton,
  GridToolbarExport,
  GridToolbarDensitySelector,
  useGridApiContext,
  GridLoadingOverlay,
  GridNoRowsOverlay,
  GridNoResultsOverlay
} from '@mui/x-data-grid';
import { Box, Button } from "@mui/material";
import { Snackbar, Alert } from "@mui/material";
import { AppProviderStore } from "../../AppStore";
import { toJS } from "mobx";
import axios from "axios";
import { genDataGridProps } from "../../lib/tableHelper.mjs";


async function setTable() {
  try {
    const res = await fetch(`/api/getAllUsers`);
    if (!res.ok) {
      throw new Error("Failed to fetch users");
    }
    return await res.json();
  } catch (error) {
    console.error("Error fetching users:", error);
    return []; // Return empty array in case of error
  }
}



function UsersTable({ category }) {
  const { AppStore } = useContext(AppProviderStore);
  const {users,getUsers} = AppStore
  const [loading, setLoading] = useState(true);
  // const currentYear = new Date().getFullYear();
  const [snackBar, setSnackbar] = useState(null);
  const handleCloseSnackbar = () => setSnackbar(null)
  const [tableData, setTableData] = useState({ columns: [], rows: [] })


  useEffect(() => {


  }, [users]);


  useEffect(() => {
    const fetchUsers = async () => {
      try {
        await getUsers()
        console.log(users)
      } catch (error) {
        console.error('Error fetching users', error)
      }
    }

    if (users && users.length > 0) {
      console.log('Users data');
      const tData = genDataGridProps('users', users);
      setTableData(tData);
    }else{
      fetchUsers()
    }
  }, [users]);

  function CustomToolbar() {
    const toggleArchived = () => {
      setIsArchived(prevState => !prevState)
    }
    return (
      <GridToolbarContainer>
        <GridToolbarColumnsButton />
        <GridToolbarFilterButton />
        <GridToolbarDensitySelector />
        <Box sx={{ flexGrow: 1 }} />
        <GridToolbarExport
          slotProps={{
            tooltip: { title: 'Export data' },
            button: { variant: 'outlined' },
          }}
        />
      </GridToolbarContainer>
    )
  }
  const handleArchiveRestoreClick = async (row, archivestate) => {
    console.log('archiverestoreclicked')
    let archiveRequest = { filter: { _id: row._id }, toUpdate: { $set: { isArchived: archivestate } } }
    console.log('before', tableData)    
    await axios.put(`/api/groups/adminUpdate`, archiveRequest)
    setTableData(prevState => ({
      ...prevState,
      rows:prevState.rows.map(curRow=>{
        if(curRow._id === row._id){
          console.log(curRow)
          console.log(row)
          return {
            ...curRow,
            isArchived:archivestate
          }
        }
      })
    }))
    console.log('after? ', tableData)
    return
  }

  const handleEdit = useCallback(async (newRow, oldRow) => {
    let groupId = newRow._id
    let groupUpdate = { filter: { _id: groupId }, toUpdate: {}, update: false }
    let trackUpdate = { filter: {}, toUpdate: {}, update: false }
    let updatedRow
    for (const key in newRow) {
      let updatedValue = newRow[key]
      if (Object.prototype.hasOwnProperty.call(newRow, key) && !Object.prototype.hasOwnProperty.call(oldRow, key)) {
        //console.log(key, 'doesnt exist in previous entry')
        if (key.includes('track')) {
          //console.log('doing something with tracks')
          let trackMatch = key.match(/track_(\d+)_(.+)/);
          let updateKey
          if (trackMatch) {
            let index = parseInt(trackMatch[1])
            trackUpdate.index = index
            let trackKey = trackMatch[2]
            let trackId = oldRow.tracks[index]._id
            trackUpdate.filter._id = trackId
            if (trackKey.includes('soloist')) {
              console.log('doing something with soloists inside tracks')
              let soloistMatch = trackKey.match(/soloist_(\d+)_(.+)/);
              let soloistIndex = parseInt(soloistMatch[1])
              let soloistKey = soloistMatch[2]
              updateKey = `soloists.${soloistIndex}.${soloistKey}`
              // updatedRow.tracks[index].soloists[soloistIndex][soloistKey] = updatedValue
            } else {
              updateKey = trackKey
            }
            console.log('finalize track update')
            trackUpdate.toUpdate = { $set: { [updateKey]: updatedValue } }
            trackUpdate.update = true
            console.log(trackUpdate)
          }
        } else if (key.includes('maskedName')) {
          //console.log('this is a nested masked name update')
          let match = parseInt(key.match(/\d+/))
          let index = match ? match : 0
          groupUpdate.toUpdate = { $set: { [`classifications.${index}.maskedName`]: newRow[key].toUpperCase() } }
          //temp till I make update (debating on if I want to do a single update or a save and update all at once)
          updatedRow.classifications[index].maskedName = newRow[key].toUpperCase()
          groupUpdate.update = true
        } else {
          //console.log('key doesnt exist in old object but isnt track related and not nested')
          groupUpdate.update = true
          groupUpdate.toUpdate = { $set: { [key]: updatedValue } }
        }


      } else if (oldRow[key] !== newRow[key]) {
        if (key === "maskedName") {
          //console.log('masked name was updated')
          updatedValue = updatedValue.toUpperCase()
        }
        groupUpdate.toUpdate = { $set: { [key]: updatedValue } }
        groupUpdate.update = true
      }
    }

    if (groupUpdate.update) {
      console.log('call updateGroup route')
      console.log(groupUpdate)
      let res = await axios.put(`/api/groups/adminUpdate`, groupUpdate)
      updatedRow = res.data
      //console.log(updatedRow)
    }

    if (trackUpdate.update) {
      console.log('call updateTrack route')
      console.log(trackUpdate)
      let res = await axios.put(`/api/tracks/adminUpdate`, trackUpdate)
      let updatedTrack = res.data
      updatedRow = { ...oldRow }
      updatedRow.tracks[trackUpdate.index] = updatedTrack
    }
    return updatedRow

  })

  const handleProcessRowUpdateError = useCallback((error) => {
    console.log('Update Erro', error)
    setSnackbar({ children: error.message, severity: 'error' })
  }, [])

  return (
    <div>
      <DataGrid
        {...tableData}
        getRowId={(row) => row._id}
        slots={{
          loadingOverlay: GridLoadingOverlay,
          toolbar: CustomToolbar,
          noRowsOverlay: GridNoRowsOverlay,
          noResultsOverlay: GridNoResultsOverlay
        }}
        processRowUpdate={handleEdit}
        onProcessRowUpdateError={handleProcessRowUpdateError}
      />

    </div>
  );
}

export default observer(UsersTable);
