import { useForm, Controller, useWatch, useFieldArray } from 'react-hook-form';
import { useEffect, useState, useContext } from 'react';
import { Typo<PERSON>, TextField, Modal, Button, FormControlLabel, Checkbox, Tooltip, IconButton, Box, CircularProgress } from '@mui/material';
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import dayjs from 'dayjs';
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { MuiFileInput } from "mui-file-input";
import AttachFileIcon from "@mui/icons-material/AttachFile";
import CancelIcon from "@mui/icons-material/Cancel";
import AddIcon from "@mui/icons-material/Add";
import axios from 'axios';
import { toJS } from 'mobx';
import Swal from "sweetalert2";
import { AppProviderStore } from '../../../AppStore';
import { observer } from 'mobx-react';

function EditSelectionForm({ defaultValues, route, id, hasSoloists, userId, handleClose }) {
    // const [soloists, setSoloists] = useState(defaultValues.soloists ? defaultValues.soloists : []);
    // console.log(id)
    const { register, unregister, handleSubmit, control, getValues, formState: { dirtyFields, touchedFields } } = useForm({ defaultValues });
    const { fields, append, remove } = useFieldArray({ shouldUnregister: true, control, name: 'soloists' })
    const year = new Date().getFullYear()
    const [loading, setLoading] = useState(false)
    const [uploadProgress, setUploadProgress] = useState({})
    const [modalOpen, setModalOpen] = useState(false);
    const { AppStore } = useContext(AppProviderStore);
    const { selectedGroup, currentTrack, updateCurrentTrack } = AppStore
    // console.log(toJS(currentTrack._id))
    const reupload = useWatch({
        control,
        name: 'reupload',
        defaultValue: false
    });

    function compareSoloists(curr, incoming) {

        if (curr.length !== incoming.length) {
            return false;
        }

        for (let i = 0; i < curr.length; i++) {
            if (curr[i].name !== incoming[i].name || curr[i].instrument !== incoming[i].instrument) {
                return false
            }
        }
        return true
    }
    const onSubmit = async (data) => {
        let toSet = {};
        let incomingDate = new Date(data.dateRecorded)
        let currDate = new Date(currentTrack.dateRecorded)
        if (incomingDate.getTime() !== currDate.getTime()) {
            toSet.dateRecorded = incomingDate.toISOString()
        }
        // Check if user is uploading a file
        for (const key in data) {
            if (key === 'file' || key === 'soloists') {
                continue
            } else {
                console.log(touchedFields[key])
                if (touchedFields[key]) {
                    console.log(key, ' was changed', data[key])
                    toSet[key] = data[key];
                }
            }
        }

        if (data?.soloists && !compareSoloists(currentTrack.soloists, data.soloists)) {
            toSet.soloists = data.soloists
        }

        if (data.reupload) {
            delete data.reupload
            let newFile = `${year}/tracks/${userId}/${selectedGroup.ensembleName}/${data.file.name}`

            setModalOpen(true)
            let presignRes = await axios.post(`/api/minioPresignedUrl`, { filename: newFile })
            let presignedUrl = presignRes.data.presigned
            let minioRes = await axios.put(presignedUrl, data.file, {
                onUploadProgress: (progressEvent) => {
                    const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total)
                    setUploadProgress({ ...uploadProgress, [data.file.name]: percentCompleted })
                }
            })
            console.log(minioRes)
            if (minioRes.status === 200) {
                setUploadProgress({ ...uploadProgress, [data.file.name]: 100 })
                toSet.minioSrc = `https://app.foundationformusiceducation.org/fme-app/${newFile}`
                setLoading(false)
                setModalOpen(false)

            } else {
                setLoading(false)
                setModalOpen(false)
                Swal.fire({
                    title: "Error",
                    text: "Something went wrong, <NAME_EMAIL> if you continue to experience issues",
                    icon: "error"
                })
                return
            }
        }
        let trackUpdate = {
            filter: { _id: id },
            toUpdate: {}
        }

        if (Object.keys(toSet).length !== 0) {
            trackUpdate.toUpdate.$set = { ...toSet }
        }
        console.log('submit data', data)
        console.log('submit updates', trackUpdate)
        try {
            let updateSuccessful = await updateCurrentTrack(trackUpdate)
            if (updateSuccessful) {
                // Only try to remove previous file if it exists and we're replacing it
                if (data.minioSrc && data.reupload) {
                    let prevFile = `${year}/tracks/${userId}/${extractFileName(data.minioSrc)}`
                    await axios.post(`/api/removeMinioObject`, { filename: prevFile })
                }
                handleClose()
                Swal.fire({
                    title: "Success!",
                    text: "Your selection has been updated successfully",
                    icon: "success",
                })
            }


        } catch (err) {
            console.error(err)
        }

    };

    const extractFileName = (minioSrc) => {
        if (!minioSrc) return 'No file uploaded';
        const parts = minioSrc.split('/');
        const fileName = parts[parts.length - 1];
        return fileName;
    };

    const renderSoloistSection = () => {
        return (
            <>
                {fields.map((soloist, index) => (
                    <div key={soloist.id} style={{ display: 'flex', alignItems: 'center', marginBottom: '20px' }}>
                        <Controller
                            name={`soloists.${index}.name`}
                            control={control}
                            defaultValue={soloist.name}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    label="Name"
                                    variant="outlined"
                                    margin="normal"
                                    fullWidth
                                />
                            )}
                        />
                        <Controller
                            name={`soloists.${index}.instrument`}
                            control={control}
                            defaultValue={soloist.instrument}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    label="Instrument"
                                    variant="outlined"
                                    margin="normal"
                                    fullWidth
                                />
                            )}
                        />
                        <Controller
                            name={`soloists.${index}.timeStamp`}
                            control={control}
                            defaultValue={soloist?.timeStamp ? soloist.timeStamp : null}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    label="Time Stamp"
                                    variant="outlined"
                                    margin="normal"
                                    fullWidth
                                />
                            )}
                        />
                        <Tooltip title="Remove Soloist" placement="left">
                            <IconButton
                                aria-label="Remove Soloist"
                                onClick={() => {
                                    remove(index);
                                }}
                            >
                                <CancelIcon color="error" />
                            </IconButton>
                        </Tooltip>
                    </div>
                ))}
            </>
        );
    };


    return (
        <>
            <form onSubmit={handleSubmit(onSubmit)}>
                <Controller
                    name="title"
                    control={control}
                    render={({ field }) => (
                        <TextField
                            {...field}
                            label="Title"
                            variant="outlined"
                            margin="normal"
                            fullWidth
                        />
                    )}
                />
                <Controller
                    name="composer"
                    control={control}
                    render={({ field }) => (
                        <TextField
                            {...field}
                            label="Composer"
                            variant="outlined"
                            margin="normal"
                            fullWidth
                        />
                    )}
                />
                <Controller
                    name="venue"
                    control={control}
                    render={({ field }) => (
                        <TextField
                            {...field}
                            label="Venue"
                            variant="outlined"
                            margin="normal"
                            fullWidth
                        />
                    )}
                />
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                    <Controller
                        control={control}
                        name="dateRecorded"
                        render={({ field }) => (
                            <DatePicker
                                label="Date of Recording"
                                value={dayjs(field.value) || null}
                                onChange={(date) => field.onChange(date)}
                                inputFormat="MM/DD/YYYY"
                                TextFieldComponent={TextField}
                            />
                        )}
                    />
                </LocalizationProvider>
                <Controller
                    name="duration"
                    control={control}
                    render={({ field }) => (
                        <TextField
                            {...field}
                            label="Duration"
                            variant="outlined"
                            margin="normal"
                            fullWidth
                        />
                    )}
                />
                {hasSoloists && (
                    <>
                        <Typography variant="subtitle1" gutterBottom>
                            Soloists
                        </Typography>
                        {renderSoloistSection()}
                        <Button
                            variant="contained"
                            color="success"
                            startIcon={<AddIcon fontSize="large" />}
                            onClick={() => append({ name: "", instrument: "" })}
                            className="w-fit px-5 py-3"
                        >
                            Add Soloist
                        </Button>
                    </>
                )}

                <TextField
                    label="Current File"
                    variant="outlined"
                    margin="normal"
                    fullWidth
                    disabled
                    value={extractFileName(getValues('minioSrc'))}
                />
                <FormControlLabel
                    control={
                        <Checkbox
                            {...register("reupload")}
                        />
                    }
                    label={getValues('minioSrc') ? "Reupload/Replace" : "Upload File"}
                />
                <div style={{ marginBottom: '20px' }} />
                {reupload && (
                    <Controller
                        name="file"
                        control={control}
                        render={({ field }) => (
                            <MuiFileInput
                                {...field}
                                InputProps={{
                                    inputProps: {
                                        accept: ".wav, .mp3",
                                    },
                                    endAdornment: <AttachFileIcon />,
                                }}
                                label="Wav File Upload"
                            />
                        )}
                    />
                )}
                <div style={{ marginBottom: '20px' }} />
                <Button
                    className='bg-iconGreen'
                    type="submit"
                    variant="contained"
                    fullWidth
                >
                    Submit
                </Button>
            </form>
            <Modal open={modalOpen} onClose={() => setModalOpen(false)}>
                <div style={{ position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', background: 'white', padding: '40px', minWidth: '400px' }}>
                    <Typography variant="h6" style={{ color: 'black' }}>Uploading...</Typography>
                    {Object.keys(uploadProgress).map((file, index) => (
                        <div key={`track_${index}`} style={{ marginTop: '20px' }}>
                            <Typography variant="body1" style={{ color: 'black' }}>{file}</Typography>
                            <Box sx={{ position: 'relative', display: 'inline-flex' }}>
                                <CircularProgress variant="determinate" value={uploadProgress[file]} style={{ color: '#2e7d32' }} />
                                <Box
                                    sx={{
                                        top: 0,
                                        left: 0,
                                        bottom: 0,
                                        right: 0,
                                        position: 'absolute',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                    }}
                                >
                                    <Typography variant="caption" component="div" color="text.primary">
                                        {`${Math.round(uploadProgress[file])}%`}
                                    </Typography>
                                </Box>
                            </Box>
                        </div>
                    ))}
                </div>
            </Modal>
        </>

    );
}

export default observer(EditSelectionForm);
