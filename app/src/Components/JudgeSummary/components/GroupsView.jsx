import React from 'react';
import {
  Box,
  Typography,
  Paper
} from '@mui/material';
import {
  Class as ClassIcon
} from '@mui/icons-material';
import BackButton from './BackButton';
import ScoringProgressCard from './ScoringProgressCard';
import TopGroupsSection from './TopGroupsSection';
import SortingControls from './SortingControls';
import GroupsTable from './GroupsTable';

const GroupsView = ({
  selectedClassification,
  processedGroups,
  sortConfig,
  requestSort,
  isAutoSelectLoading,
  handleAutoSelectTopGroups,
  handleMovesOnChange,
  handleGroupSelect,
  onBackToClassificationSummary,
  onBackToSummary
}) => {
  if (!selectedClassification || !selectedClassification.groups) {
    return (
      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="body1">
          No groups found in this classification.
        </Typography>
      </Paper>
    );
  }

  // Calculate scoring statistics
  const totalGroups = processedGroups.length;
  const scoredGroups = processedGroups.filter(group => group.hasScore).length;
  const movesOnGroups = processedGroups.filter(group => group.movesOn).length;
  const progress = totalGroups > 0 ? (scoredGroups / totalGroups) * 100 : 0;

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', gap: 2 }}>
        <BackButton
          onClick={onBackToClassificationSummary}
          text="Back to Classification Summary"
        />
        <BackButton
          onClick={onBackToSummary}
          text="Back to Summary"
        />
      </Box>

      <Typography variant="h5" sx={{ mb: 3, color: '#3b8c6e', display: 'flex', alignItems: 'center' }}>
        <ClassIcon sx={{ mr: 1 }} />
        {selectedClassification.id?.name || 'Classification'} Groups
      </Typography>

      {/* Classification Summary Card */}
      <ScoringProgressCard 
        totalGroups={totalGroups}
        scoredGroups={scoredGroups}
        progress={progress}
        phase={selectedClassification.phase}
        movesOnGroups={movesOnGroups}
      />

      {/* Top 50% Groups Section - Only show for Phase 1 */}
      {selectedClassification.phase === 1 && (
        <TopGroupsSection 
          sortedGroups={processedGroups}
          isAutoSelectLoading={isAutoSelectLoading}
          handleAutoSelectTopGroups={handleAutoSelectTopGroups}
        />
      )}

      {/* Sorting Controls */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="body1">
          Select a group to begin scoring.
        </Typography>

        <SortingControls 
          sortConfig={sortConfig}
          requestSort={requestSort}
          showMovesOnSort={selectedClassification.phase === 1}
        />
      </Box>

      {/* Group Table */}
      <GroupsTable 
        groups={processedGroups}
        handleGroupSelect={handleGroupSelect}
        handleMovesOnChange={handleMovesOnChange}
        showMovesOn={selectedClassification.phase === 1}
        showActionButton={true}
      />
    </Box>
  );
};

export default GroupsView;
