import Judge from "../models/judge.mjs"
import Group from "../models/group.mjs";

export default async function (fastify, opts) {
    fastify.get("/api/judges", async (request, reply) => {
        if (request.query.id) {
            const judge = await Judge.findById(request.query.id)
                .populate(
                    {
                        path: 'contests',
                        select: 'name'
                    })
                .populate(
                    {
                        path: "categories",
                        select: "name",
                    })
                .populate({
                    path: "classifications.classification",
                    select: "name",
                })
                .populate(
                    {
                        path: "groups",
                    })
            reply.send(judge)
        } else {
            let filter = { isActive: true }
            if (request.query.viewAll) {
                filter = {}
            }
            const judges = await Judge.find(filter)
                .populate({
                    path: "assigned.category",
                    select: "name",
                })
                .populate({
                    path: 'assigned.classifications.id',
                    select: "name",
                })
                .populate({
                    path:'assigned.classifications.groups',
                })
            reply.send(judges)
        }
    })

    fastify.post("/api/judges/add", async (request, reply) => {
        const judges = request.body
        const newJudges = await Judge.insertMany(judges)
        reply.code(201).send({ success: true, judges: newJudges })
    })

    fastify.put("/api/judges/remove/:judgeId", async (request, reply) => {
      //update where this is called to use the new route
        console.log('route hit?');
        const { judgeId } = request.params;
        try {
            const judge = await Judge.findById(judgeId)
            if (!judge) {
                return reply.status(404).send({ error: "Judge not found" })
            }
            judge.isActive = false;
            await judge.save();
        } catch (error) {
            console.error(error);
            return reply.status(500).send({ error: "Internal Server Error" })
        }
    })
    
    fastify.put("/api/judges/edit/:judgeId", async (request, reply) => {
        const { judgeId } = request.params;
        const updateData = request.body;

        try {
            const judge = await Judge.findById(judgeId);
            if (!judge) {
                return reply.status(404).send({ error: "Judge not found" });
            }

            // Update basic fields
            judge.name = updateData.name;
            judge.email = updateData.email;
            
            // Update assignments
            if (updateData.assigned) {
                judge.assigned = updateData.assigned.map(assignment => ({
                    category: assignment.category,
                    classifications: assignment.classifications.map(classification => ({
                        id: classification.id,
                        phase: classification.phase,
                        groups: classification.groups || []
                    }))
                }));
            }

            const updatedJudge = await judge.save();

            // Populate the response data
            const populatedJudge = await Judge.findById(updatedJudge._id)
                .populate({
                    path: "assigned.category",
                    select: "name",
                })
                .populate({
                    path: 'assigned.classifications.id',
                    select: "name",
                })
                .populate({
                    path: 'assigned.classifications.groups'
                });
            console.log(populatedJudge)
            reply.code(200).send({ success: true, judge: populatedJudge });
        } catch (error) {
            console.error('Error updating judge:', error);
            reply.code(500).send({ error: "Internal Server Error" });
        }
    });

    fastify.post("/api/judges/:judgeId/judgeIntro", async function (request, reply) {
        const { judgeId } = request.params;
        const uri = request.body.uri;
        //future judge update if assigned multiple categories (only one judge at the moment has this)
        //const index = request.body.index
        try {
            const updatedJudge = await Judge.findOneAndUpdate({ _id: judgeId }, { judgeIntro: uri }, { new: true })
            reply.send(updatedJudge).code(200)
        } catch (error) {
            console.log(error)
        }
    })

    fastify.get("/api/judges/:judgeId/getAssignments", async (request, reply) => {
        const { judgeId } = request.params;
      
        try {
          // Check if judgeId is a valid ObjectId
          if (!judgeId || judgeId === 'null') {
            return reply.code(400).send({ error: 'Invalid judgeId' });
          }
      
          // Step 1: Fetch judge data with necessary populations
          let judgeData = await Judge.findById(judgeId)
            .populate({
              path: "assigned.category",
              select: "name",
            })
            .populate({
              path: 'assigned.classifications.id',
              select: "name",
            })
            .populate({
              path: 'assigned.classifications.groups',
              model:'Groups',
              populate: {
                path: 'tracks',
                model: 'Tracks', 
              }
            });

            
          if (!judgeData) {
            return reply.code(404).send({ error: 'Judge not found' });
          }
      
          // Step 2: Check and populate groups if missing and phase is not 2
          const currentYear = new Date().getFullYear();
      
          for (let assignment of judgeData.assigned) {
            for (let classification of assignment.classifications) {
              if (!classification.groups || classification.groups.length === 0) {
                if (classification.phase !== 2) {
                  // Fetch groups with the same category ID and classification ID in the current year
                  let groups = await Group.find({
                    category: assignment.category,
                    isArchived: false,
                    $or: [
                      { 'classifications.id': classification.id },
                      { classification: classification.id }
                    ],
                    year: currentYear
                  }).populate({
                    path: 'tracks',
                    model: 'Tracks'
                  });
                  console.log(groups.length)
                  // Populate the groups field
                  classification.groups = groups;
                } else {
                  // For phase 2, log that groups will be manually added later
                  console.log(`Skipping group fetch for classification ID ${classification.id} as phase is 2`);
                }
              }
            }
          }
      
          console.log(judgeData);
      
          reply.code(200).send(judgeData);
        } catch (error) {
          console.error(error);
          reply.code(500).send({ error: 'An error occurred while fetching judge assignments' });
        }
      });

}
