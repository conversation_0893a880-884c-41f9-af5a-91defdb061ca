import Stripe from 'stripe';

let stripe
if(process.env.NODE_ENV === 'dev'){
    stripe = Stripe(process.env.STRIPE_SK_TEST)
}else{
    stripe = Stripe(process.env.STRIPE_SK_LIVE)
}

export default async function (fastify,opts){
    

    fastify.post(`/api/payment_intent`, async (request, reply) => {
        console.log('payment_intent', request.body)
        const { amount, userEmail, schoolName, invoiceNumber, invoiceId } = request.body
        try {
            const paymentIntent = await stripe.paymentIntents.create({
                amount: amount,
                currency: 'usd',
                metadata: { userEmail, schoolName, invoiceNumber, invoiceId },
                payment_method_types: ['card'], // Restrict to cards only
            });

            return reply.send({ clientSecret: paymentIntent.client_secret });
        } catch (error) {
            return reply.status(500).send({ error: error.message });
        }
    });
}