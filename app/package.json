{"name": "fme-app", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@ffmpeg/core": "^0.12.6", "@ffmpeg/ffmpeg": "^0.12.10", "@fortawesome/react-fontawesome": "^0.2.0", "@google-cloud/local-auth": "^2.1.0", "@google-cloud/storage": "^6.11.0", "@hookform/resolvers": "^5.0.1", "@mui/icons-material": "^5.15.11", "@mui/material": "^5.15.11", "@mui/x-data-grid": "^7.3.1", "@mui/x-date-pickers": "^6.19.5", "@stripe/react-stripe-js": "^3.3.0", "@stripe/stripe-js": "^5.10.0", "@tailwindcss/line-clamp": "^0.4.2", "@tanstack/react-table": "^8.9.2", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@textea/json-viewer": "^3.0.0", "album-art": "^3.1.0", "axios": "^1.3.4", "classnames": "^2.3.2", "dayjs": "^1.11.10", "file-type": "^19.0.0", "gravatar-url": "^4.0.1", "https": "^1.0.0", "jwt-decode": "^4.0.0", "logrocket": "^4.0.3", "mobx": "^6.9.0", "mobx-react": "^7.6.0", "mui-file-input": "^4.0.4", "npm-watch": "^0.11.0", "react": "^18.2.0", "react-audio-voice-recorder": "^1.0.4", "react-dom": "^18.2.0", "react-hook-form": "^7.44.3", "react-icons": "^4.7.1", "react-router-dom": "^6.8.1", "react-scripts": "5.0.1", "react-select": "^5.8.0", "react-table": "^7.8.0", "react-to-print": "^2.14.13", "react-toggle": "^4.1.3", "react-webcam": "^7.0.1", "sweetalert2": "^11.7.27", "web-vitals": "^2.1.4", "zod": "^3.24.4"}, "browser": {"child_process": false}, "scripts": {"watch": "npm-watch", "start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "watch": {"build": {"patterns": ["src"], "extensions": "js,jsx"}}, "devDependencies": {"@hookform/devtools": "^4.3.1", "autoprefixer": "^10.4.13", "postcss": "^8.4.21", "tailwindcss": "^3.2.7"}}