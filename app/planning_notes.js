/**
 * Data Structure Concerns:
 * Current schema allowed for a judge to be assigned an array of contests, categories, classifications
 * Problem with this is if they are assigned only certain classifications of one category but all of another that share classifications
 * Leading to issues of getting all or only part of the groups inside that 
 * Proposed change:
 */

let judge = {
    name: "<PERSON>",
    email: "<EMAIL>",
    assignments:[
        {
            contest: "contestId",
            category: "categoryId",
            singleJudge: false, //if true dont need classifications array
            classifications:[
                {
                    classification: "classificationId",
                    phase: 1 //1 = all groups unless noted 2= "Top 50%"
                    //do we want groups here? 
                }    
            ]
        },
        {
            contest: "contestId",
            category: "categoryId",
            singleJudge: true
        }
    ]
}

/**
 * Other things of note - 
 * How do we want to handle a category-classification that gets split in phase 1 
 * How do we want to handle a judge's uri that gets assigned to two different categories
 *     - Do we want two links ? 
 *     - Change the structure 
 *         from:
 *          /{contest}/{category}/?judge=1234&salt=29438234
 *         To:
 *          /adjudicator
 *           
 */

/**
 * Paste one or more documents here
 */
[
    {
      "_id": {
        "$oid": "6480838d8a1ffa1377aee8e1"
      },
      "name": "<PERSON>",
      "contests": [
        {
          "$oid": "64345bd6dd98890aad8cef3c"
        }
      ],
      "classifications": [
        {
          "classification": {
            "$oid": "643453f8dd98890aad8cef21"
          },
          "phase": 1
        },
        {
          "classification": {
            "$oid": "643453f8dd98890aad8cef24"
          },
          "phase": 2
        }
      ],
      "groups": [],
      "uri": null
    },
    {
      "name": "Anthony Maiello",
      "contests": [
        {
          "$oid": "64345bd6dd98890aad8cef3c"
        },
        {
          "$oid":"64345c8fdd98890aad8cef3d"
        }
      ],
      "categories":[
        {
            "$oid":"643456dbdd98890aad8cef33"
        },
        {
                 "$oid":"643456dbdd98890aad8cef2f", 
        }
      ],
      "classifications": [
        {
          "classification": {
            "$oid": "643453f8dd98890aad8cef1b"
          },
          "phase": 1
        },
        {
          "classification": {
            "$oid": "643453f8dd98890aad8cef1c"
          },
          "phase": 2
        },
        {
          "classification":{
            "$oid":"643453f8dd98890aad8cef1b"
          }
        },
              {
          "classification":{
            "$oid":"643453f8dd98890aad8cef1c"
          }
        },
              {
          "classification":{
            "$oid":"643453f8dd98890aad8cef1e"
          }
        },
              {
          "classification":{
            "$oid":"643453f8dd98890aad8cef1d"
          }
        },
      ],
      "groups": [],
      "uri": null
    },
      {
      "name": "Brian West",
      "contests": [
        {
          "$oid": "64345bd6dd98890aad8cef3c"
        }
      ],
      "classifications": [
        {
          "classification": {
            "$oid": "643453f8dd98890aad8cef21"
          },
          "phase": 1
        },
        {
          "classification": {
            "$oid": "643453f8dd98890aad8cef24"
          },
          "phase": 2
        }
      ],
      "groups": [],
      "uri": null
    }
  ]