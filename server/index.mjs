import Fastify from "fastify";
import autoLoad from "@fastify/autoload";
import cors from "@fastify/cors";
import multipart from "@fastify/multipart";
import { fileURLToPath } from "url";
import { dirname, join } from "path";
import mongoose from "mongoose";

const server = Fastify({
  logger: true,
  bodyLimit: 524288000
});
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
await server.register(cors, {
  origin: ['http://localhost:32023'],
  methods: ['GET', 'PUT', 'PATCH', 'POST', 'DELETE']
});
await server.register(multipart)

server.register(autoLoad, {
  dir: join(__dirname, "api"),
});


server.get("/api/tests", async function (request, reply) {
  reply.send("Hello");
});

const start = async () => {
  try {
    console.log("connecting to db");
    await mongoose.connect("mongodb://mongo:27017/fme");
    mongoose.set("debug", true);

    server.listen({ host: "0.0.0.0", port: 3000 });
  } catch (error) {
    server.log.error(err);
    process.exit(1);
  }
};

start();
