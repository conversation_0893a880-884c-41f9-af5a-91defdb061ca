import { AppProviderStore } from "../AppStore"
import { observer } from "mobx-react"
import { useContext, useEffect, useState } from "react"
import RegistrationForm from "../Components/RegistrationViewComponents/RegistrationForm"


function RegistrationView({toggleRegistrationView}) {
    const { AppStore } = useContext(AppProviderStore)
    const { getFormOptions } = AppStore

    useEffect(() => {
        getFormOptions()
    }, [])



    return (
        <RegistrationForm toggleRegistrationView={toggleRegistrationView}/>
    );
}
export default observer(RegistrationView)