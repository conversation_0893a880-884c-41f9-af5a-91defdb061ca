import React, { useState } from 'react';

function DownloadCodes() {
    const [message, setMessage] = useState('');

    const handleFileChange = async (event) => {
        const file = event.target.files[0];
        if (file) {
            const formData = new FormData();
            formData.append('csv', file);
            
            try {
                const response = await fetch('/api/groups/uploadCodes', {
                    method: 'POST',
                    body: formData,
                });

                if (!response.ok) {
                    throw new Error(`Server responded with ${response.status}`);
                }

                const result = await response.text();
                console.log(result)
                setMessage(result);
            } catch (error) {
                console.error("There was an error uploading the file:", error);
                setMessage(`Error: ${error.message}`);
            }
        }
    };

    return (
        <div>
            <input type="file" accept=".csv" onChange={handleFileChange} />
            <p>{message}</p>
        </div>
    );
}
export default DownloadCodes;