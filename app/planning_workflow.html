<script>

//Who needs URIs?

//Groups
//What do groups need to see

//Judge intro video
//Memos
//Question Forms / Notes
//Score
//What awards did I get?

//Judges

//Screen to create intro video
//What Groups am I Judging?
//What ones have I completed?
//What info did I put in for this group?
//Contest Classification Overall List Ranking That They are assigned to?

//Admin

//What contests are we running
//Who is judging them?
//Or maybe simply, a super judge view


//Example URI Vars
var uri = {
    judge: "john",
    group: "PHHS",
}

//if judge set show judge view, if group set show group view only

//if judge set to Admin user show extra stuff?

</script>

<h1>Group</h1>
https://localhost/mark-of-excellence/national-choral-honors/canyon-vista-middle-school-chamber-orchestra/
https://localhost/{contest}/{category}/{ensambleName}

<h1>Judge</h1>
https://localhost/mark-of-excellence/national-choral-honors/?judge=123456&key=123456
https://localhost/mark-of-excellence/national-choral-honors/?judge=123456&key=123456&groupId=123456


<h1>Admin</h1> //basic auth
https://localhost/admin/?judge=123456&key=123456
