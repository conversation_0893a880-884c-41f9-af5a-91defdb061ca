import { Routes, Route, BrowserRouter } from "react-router-dom";
import './App.css'
import Home from "./Pages/Home"
import Judge from "./Pages/Judge"
import Admin from "./Pages/Admin"
import Results from "./Pages/Results"
import Groups from "./Pages/Groups"
import Account from "./Pages/Account"
import {AppProvider} from "./AppStore";
import YoboMobxDevTools from "./Components/TestComponents/YoboMobxDevtools";
import LogRocket from 'logrocket';

function App() {
  LogRocket.init('3lra7u/fme-app');
  return (
    <AppProvider>
      <YoboMobxDevTools>
          <BrowserRouter>
            <Routes>
              <Route path="/" element={<Home passwordReset={false}/>} />
              <Route path="/login" element={<Home passwordReset={false} login={true} register={false}/>} />
              <Route path="/register" element={<Home passwordReset={false} login={false} register={true}/>} />
              <Route path="/judge" element={<Judge />} />
              <Route path="/admin" element={<Admin />} />
              <Route path="/results" element={<Results />} />
              <Route path="/groups" element={<Groups />} />
              <Route path="/account" element={<Account />} /> 
              <Route path="/reset-password" element={<Home passwordReset={true} />} /> 
            </Routes>
          </BrowserRouter>
      </YoboMobxDevTools>
    </AppProvider>
  );
}
export default App;
