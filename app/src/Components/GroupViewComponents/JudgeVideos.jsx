import { useEffect, useState, useContext } from "react";
import { observer } from "mobx-react"
import { AppProviderStore } from "../../AppStore";
import { toJS } from "mobx";

function JudgeVideos() {
    const { AppStore } = useContext(AppProviderStore)
    const intros = toJS(AppStore.judgeIntros);  // Convert to plain JS object

    return (
        <div>
            {intros.map((classificationItem) => (
                <div key={classificationItem.classification}>
                    <h3>{classificationItem.classification}</h3>
                    {classificationItem.intros.map((judge) => (
                        <div key={judge._id}>
                            <p>{judge.name}</p>
                            <video controls width="320" height="240">
                                <source src={judge.judgeIntro} type="video/webm" />
                                Your browser does not support the video tag.
                            </video>
                        </div>
                    ))}
                </div>
            ))}
        </div>
    );
}

export default observer(JudgeVideos);