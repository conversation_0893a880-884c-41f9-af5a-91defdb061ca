import { useState, useEffect } from "react";
import { useFormContext } from "react-hook-form";
import {
  Box,
  Stepper,
  Step,
  StepLabel,
  Button,
  Typography,
  Paper,
  Alert
} from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import CheckIcon from "@mui/icons-material/Check";

// Step components
import DirectorStep from "./FormSteps/DirectorStep";
import EnsembleStep from "./FormSteps/EnsembleStep";
import ContestStep from "./FormSteps/ContestStep";
import ContactStep from "./FormSteps/ContactStep";
import MusicStep from "./FormSteps/MusicStep";
import ReviewStep from "./FormSteps/ReviewStep";

const steps = [
  "Director Information",
  "Ensemble & School",
  "Contest Details",
  "Contact Information",
  "Music Selections",
  "Review & Submit"
];

function MultiStepForm({ groupNumber, onSubmit, validationErrors, rulesContent }) {
  const [activeStep, setActiveStep] = useState(0);
  const [stepValidationErrors, setStepValidationErrors] = useState([]);
  const [showStepError, setShowStepError] = useState(false);
  const { trigger, watch, formState: { errors } } = useFormContext();

  // Reset step errors and scroll to top when changing steps
  useEffect(() => {
    setShowStepError(false);
    setStepValidationErrors([]);

    // Scroll to the top of the page when the step changes
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  }, [activeStep]);

  // Collect errors for the current step
  const collectStepErrors = (stepErrors) => {
    const errorList = [];

    // Extract error messages without field names
    const extractErrors = (obj, path = '') => {
      if (!obj) return;

      if (obj.message) {
        // Just push the error message without the field name
        errorList.push(obj.message);
      } else if (typeof obj === 'object') {
        Object.entries(obj).forEach(([key, value]) => {
          const newPath = path ? `${path}.${key}` : key;
          extractErrors(value, newPath);
        });
      }
    };

    extractErrors(stepErrors);
    return errorList;
  };

  // Validate fields for the current step
  const validateStep = async () => {
    // Skip validation for the review step
    if (activeStep === steps.length - 1) {
      return true;
    }

    let isValid = true;
    let fieldsToValidate = [];

    // Define fields to validate for each step
    switch (activeStep) {
      case 0: // Director Information
        fieldsToValidate = [
          `group_${groupNumber}.directors`
        ];
        break;
      case 1: // Ensemble & School
        fieldsToValidate = [
          `group_${groupNumber}.ensembleName`,
          `group_${groupNumber}.schoolName`,
          `group_${groupNumber}.schoolEnrollment`
        ];
        break;
      case 2: // Contest Details
        fieldsToValidate = [
          `group_${groupNumber}.contest`,
          `group_${groupNumber}.category`,
          `group_${groupNumber}.classification`
        ];
        break;
      case 3: // Contact Information
        fieldsToValidate = [
          `group_${groupNumber}.cellphone`,
          `group_${groupNumber}.mailingAddress`
        ];
        break;
      case 4: // Music Selections
        // Get the number of tracks based on the selected category
        const selectedCategory = watch(`group_${groupNumber}.category`);
        const selectedContest = watch(`group_${groupNumber}.contest`);
        let numberOfTracks = 2; // Default
        let hasOptionalTrack = false;

        // Define category and contest IDs
        const choralId = "64345656dd98890aad8cef2d";
        const jazzId = "643456dbdd98890aad8cef2e";
        const percussionId = "643456dbdd98890aad8cef30";
        const citationId = "64345c8fdd98890aad8cef3d";

        if (selectedCategory === choralId) {
          numberOfTracks = 3;
          hasOptionalTrack = false;
        } else if (selectedCategory === jazzId || selectedCategory === percussionId) {
          numberOfTracks = 3;
          hasOptionalTrack = true;
        }

        // Create an array of fields to validate for each track
        fieldsToValidate = [];

        // Determine if soloists are allowed
        let hasSoloist = false;
        if (selectedContest !== citationId) {
          if (selectedCategory !== choralId && selectedCategory !== percussionId) {
            hasSoloist = true;
          }
        }

        // Always validate the first two tracks (required for all categories)
        for (let i = 0; i < 2; i++) {
          fieldsToValidate.push(
            `group_${groupNumber}.tracks.${i}.title`,
            `group_${groupNumber}.tracks.${i}.composer`,
            `group_${groupNumber}.tracks.${i}.duration`,
            `group_${groupNumber}.tracks.${i}.venue`,
            `group_${groupNumber}.tracks.${i}.file`
          );

          // If soloists are enabled, validate any existing soloists
          if (hasSoloist) {
            // Get the soloists array for this track
            const trackSoloists = watch(`group_${groupNumber}.tracks.${i}.soloists`);

            // If there are soloists, validate each one's required fields
            if (trackSoloists && trackSoloists.length > 0) {
              trackSoloists.forEach((_, soloistIndex) => {
                fieldsToValidate.push(
                  `group_${groupNumber}.tracks.${i}.soloists.${soloistIndex}.name`,
                  `group_${groupNumber}.tracks.${i}.soloists.${soloistIndex}.instrument`
                );
              });
            }
          }
        }

        // For the third track, only validate if it's not optional or if it has data
        if (numberOfTracks > 2) {
          // If the third track is not optional, always validate it
          if (!hasOptionalTrack) {
            fieldsToValidate.push(
              `group_${groupNumber}.tracks.2.title`,
              `group_${groupNumber}.tracks.2.composer`,
              `group_${groupNumber}.tracks.2.duration`,
              `group_${groupNumber}.tracks.2.venue`,
              `group_${groupNumber}.tracks.2.file`
            );

            // If soloists are enabled, validate any existing soloists for the third track
            if (hasSoloist) {
              const track3Soloists = watch(`group_${groupNumber}.tracks.2.soloists`);

              if (track3Soloists && track3Soloists.length > 0) {
                track3Soloists.forEach((_, soloistIndex) => {
                  fieldsToValidate.push(
                    `group_${groupNumber}.tracks.2.soloists.${soloistIndex}.name`,
                    `group_${groupNumber}.tracks.2.soloists.${soloistIndex}.instrument`
                  );
                });
              }
            }
          } else {
            // For optional track, only validate if any field has data
            const track2Title = watch(`group_${groupNumber}.tracks.2.title`);
            const track2Composer = watch(`group_${groupNumber}.tracks.2.composer`);
            const track2Duration = watch(`group_${groupNumber}.tracks.2.duration`);
            const track2Venue = watch(`group_${groupNumber}.tracks.2.venue`);
            const track2File = watch(`group_${groupNumber}.tracks.2.file`);

            // If any field has data, validate all fields
            if (track2Title || track2Composer || track2Duration || track2Venue || track2File) {
              fieldsToValidate.push(
                `group_${groupNumber}.tracks.2.title`,
                `group_${groupNumber}.tracks.2.composer`,
                `group_${groupNumber}.tracks.2.duration`,
                `group_${groupNumber}.tracks.2.venue`,
                `group_${groupNumber}.tracks.2.file`
              );

              // If soloists are enabled, validate any existing soloists for the third track
              if (hasSoloist) {
                const track3Soloists = watch(`group_${groupNumber}.tracks.2.soloists`);

                if (track3Soloists && track3Soloists.length > 0) {
                  track3Soloists.forEach((_, soloistIndex) => {
                    fieldsToValidate.push(
                      `group_${groupNumber}.tracks.2.soloists.${soloistIndex}.name`,
                      `group_${groupNumber}.tracks.2.soloists.${soloistIndex}.instrument`
                    );
                  });
                }
              }
            }
          }
        }
        break;
      default:
        break;
    }

    // Trigger validation for the fields in the current step
    isValid = await trigger(fieldsToValidate);

    if (!isValid) {
      // Collect errors for the current step
      const stepErrors = {};

      // Special handling for the Music Selection step (step 4)
      if (activeStep === 4) {
        // Get all track-related errors from the form state
        const trackErrors = errors[`group_${groupNumber}`]?.tracks;
        if (trackErrors) {
          // Add each track error to the step errors
          Object.entries(trackErrors).forEach(([trackIndex, trackError]) => {
            Object.entries(trackError).forEach(([fieldName, error]) => {
              // Handle soloists separately
              if (fieldName === 'soloists' && error) {
                // Add each soloist error
                Object.entries(error).forEach(([soloistIndex, soloistError]) => {
                  Object.entries(soloistError).forEach(([soloistField, soloistFieldError]) => {
                    const fieldPath = `group_${groupNumber}.tracks.${trackIndex}.soloists.${soloistIndex}.${soloistField}`;
                    stepErrors[fieldPath] = soloistFieldError;
                  });
                });
              } else {
                // Handle regular track fields
                const fieldPath = `group_${groupNumber}.tracks.${trackIndex}.${fieldName}`;
                stepErrors[fieldPath] = error;
              }
            });
          });
        }
      } else {
        // For other steps, use the standard approach
        fieldsToValidate.forEach(field => {
          // Handle nested fields by traversing the error object
          const fieldParts = field.split('.');
          let fieldError = errors;

          // Navigate through the error object based on the field path
          for (const part of fieldParts) {
            fieldError = fieldError?.[part];
            if (!fieldError) break;
          }

          if (fieldError) {
            stepErrors[field] = fieldError;
          }
        });
      }

      const errorList = collectStepErrors(stepErrors);
      setStepValidationErrors(errorList);
      setShowStepError(true);
    }

    return isValid;
  };

  const handleNext = async () => {
    // If we're going to the review step (last step), skip validation
    // since all steps should have been validated already
    if (activeStep === steps.length - 2) {
      // Scroll to top before changing the step
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });

      setActiveStep((prevActiveStep) => prevActiveStep + 1);
      return;
    }

    // For all other steps, validate before proceeding
    const isValid = await validateStep();

    if (isValid) {
      // Scroll to top before changing the step
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });

      setActiveStep((prevActiveStep) => prevActiveStep + 1);
    }
  };

  const handleBack = () => {
    // Scroll to top before changing the step
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });

    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleSubmit = async () => {
    // Validate all fields before submitting to identify any remaining issues
    const isValid = await trigger();

    // Scroll to top regardless of validation result
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });

    if (isValid) {
      // If validation passes, proceed with submission
      onSubmit();
    } else {
      // If validation fails, show detailed error information
      console.log("Validation errors:", errors);

      // Collect all errors to display to the user
      const allErrors = [];

      // Function to recursively extract error messages from the errors object
      const extractErrorMessages = (obj, path = '') => {
        if (!obj) return;

        if (obj.message) {
          allErrors.push(`${path ? path + ': ' : ''}${obj.message}`);
        } else if (typeof obj === 'object') {
          Object.entries(obj).forEach(([key, value]) => {
            const newPath = path ? `${path}.${key}` : key;
            extractErrorMessages(value, newPath);
          });
        }
      };

      extractErrorMessages(errors);

      // Determine which step has errors
      const getStepWithErrors = () => {
        // Check if there are director errors (step 0)
        if (errors?.[`group_${groupNumber}`]?.directors) {
          return 0;
        }

        // Check if there are ensemble errors (step 1)
        if (errors?.[`group_${groupNumber}`]?.ensembleName ||
            errors?.[`group_${groupNumber}`]?.schoolName ||
            errors?.[`group_${groupNumber}`]?.schoolEnrollment) {
          return 1;
        }

        // Check if there are contest details errors (step 2)
        if (errors?.[`group_${groupNumber}`]?.contest ||
            errors?.[`group_${groupNumber}`]?.category ||
            errors?.[`group_${groupNumber}`]?.classification) {
          return 2;
        }

        // Check if there are contact information errors (step 3)
        if (errors?.[`group_${groupNumber}`]?.cellphone ||
            errors?.[`group_${groupNumber}`]?.mailingAddress ||
            errors?.[`group_${groupNumber}`]?.shippingAddress) {
          return 3;
        }

        // Check if there are music selection errors (step 4)
        if (errors?.[`group_${groupNumber}`]?.tracks) {
          return 4;
        }

        return -1; // No specific step identified
      };

      const errorStep = getStepWithErrors();
      const errorStepName = errorStep >= 0 ? steps[errorStep] : null;

      // Display the errors to the user with guidance on which step to fix
      setShowStepError(true);
      if (errorStepName) {
        setStepValidationErrors([
          `There are validation errors in the "${errorStepName}" step. Please go back and fix these issues.`,
          ...allErrors
        ]);
      } else {
        setStepValidationErrors(allErrors.length > 0
          ? allErrors
          : ["There are validation errors. Please review all steps and fix any issues."]);
      }
    }
  };

  // Render the current step content
  const getStepContent = (step) => {
    switch (step) {
      case 0:
        return <DirectorStep groupNumber={groupNumber} />;
      case 1:
        return <EnsembleStep groupNumber={groupNumber} />;
      case 2:
        return <ContestStep groupNumber={groupNumber} />;
      case 3:
        return <ContactStep groupNumber={groupNumber} />;
      case 4:
        return <MusicStep groupNumber={groupNumber} />;
      case 5:
        return <ReviewStep
          groupNumber={groupNumber}
          rulesContent={rulesContent}
          onNavigateToStep={(step) => {
            setActiveStep(step);
            // Scroll to top when navigating
            window.scrollTo({
              top: 0,
              behavior: 'smooth'
            });
          }}
        />;
      default:
        return "Unknown step";
    }
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Stepper activeStep={activeStep} alternativeLabel sx={{ mb: 4 }}>
        {steps.map((label) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
          </Step>
        ))}
      </Stepper>

      {showStepError && stepValidationErrors.length > 0 && (
        <Alert
          severity="error"
          sx={{ mb: 2 }}
          action={
            activeStep === steps.length - 1 && ( // Only show on review step
              <Button
                color="inherit"
                size="small"
                onClick={() => {
                  // Find which step has errors
                  const errorStep = (() => {
                    // Check if there are director errors (step 0)
                    if (errors?.[`group_${groupNumber}`]?.directors) {
                      return 0;
                    }

                    // Check if there are ensemble errors (step 1)
                    if (errors?.[`group_${groupNumber}`]?.ensembleName ||
                        errors?.[`group_${groupNumber}`]?.schoolName ||
                        errors?.[`group_${groupNumber}`]?.schoolEnrollment) {
                      return 1;
                    }

                    // Check if there are contest details errors (step 2)
                    if (errors?.[`group_${groupNumber}`]?.contest ||
                        errors?.[`group_${groupNumber}`]?.category ||
                        errors?.[`group_${groupNumber}`]?.classification) {
                      return 2;
                    }

                    // Check if there are contact information errors (step 3)
                    if (errors?.[`group_${groupNumber}`]?.cellphone ||
                        errors?.[`group_${groupNumber}`]?.mailingAddress ||
                        errors?.[`group_${groupNumber}`]?.shippingAddress) {
                      return 3;
                    }

                    // Check if there are music selection errors (step 4)
                    if (errors?.[`group_${groupNumber}`]?.tracks) {
                      return 4;
                    }

                    return 0; // Default to first step if no specific errors found
                  })();

                  // Navigate to the step with errors
                  setActiveStep(errorStep);

                  // Scroll to top
                  window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                  });
                }}
              >
                Go to Error
              </Button>
            )
          }
        >
          <Typography variant="subtitle1" fontWeight="bold">Please fix the following errors:</Typography>
          <ul>
            {stepValidationErrors.map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        </Alert>
      )}

      {validationErrors.length > 0 && (
        <Alert severity="error" sx={{ mb: 2 }}>
          <Typography variant="subtitle1" fontWeight="bold">Form Validation Errors:</Typography>
          <ul>
            {validationErrors.map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        </Alert>
      )}

      <Paper
        elevation={3}
        sx={{
          p: 3,
          mb: 3,
          minHeight: '400px',
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        {getStepContent(activeStep)}
      </Paper>

      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
        <Button
          color="inherit"
          disabled={activeStep === 0}
          onClick={handleBack}
          startIcon={<ArrowBackIcon />}
          sx={{ mr: 1 }}
        >
          Back
        </Button>
        <Box sx={{ flex: '1 1 auto' }} />
        {activeStep === steps.length - 1 ? (
          <Button
            onClick={handleSubmit}
            variant="contained"
            endIcon={<CheckIcon />}
            sx={{
              bgcolor: '#008544',
              '&:hover': { bgcolor: '#006834' }
            }}
          >
            Submit
          </Button>
        ) : (
          <Button
            onClick={handleNext}
            variant="contained"
            endIcon={<ArrowForwardIcon />}
            sx={{
              bgcolor: '#008544',
              '&:hover': { bgcolor: '#006834' }
            }}
          >
            Next
          </Button>
        )}
      </Box>
    </Box>
  );
}

export default MultiStepForm;
