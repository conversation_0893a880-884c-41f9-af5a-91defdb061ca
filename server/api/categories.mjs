import Category from "../models/category.mjs";
import Judge from "../models/judge.mjs";


export default async function (fastify, opts) {
  fastify.get("/api/categories", async function (request, reply) {
    if (request.query.name) {
      const contest = await Category.findOne(
        {
          name: request.query.name,
        },
        "_id"
      );
      reply.send(contest._id);
    } else {
      console.log("attempting to get contests");
      const categories = await Category.find()
        .populate("classifications", { name: 1, _id: 1 })
        .lean();

      reply.send(categories);
    }
  });

  fastify.post("/api/categories/:categoryId/judgeIntros",async function(request,reply){
    const { categoryId } = request.params;
    const { uri } = request.body;
    try{
      const updatedCategory = await Category.findOneAndUpdate(
        { _id: categoryId },
        { $push: { judgeIntros: uri } },
        { new: true }
      );
      //might want to add a flag to a judge to mark that they have submitted a video
      // const updatedJudge = await Judge.findOneAndUpdate({_id:judgeId})
      reply.send(updatedCategory).code(200)
    }catch(error){
      console.log(error)
    }

  })
}
