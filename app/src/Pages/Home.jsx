import { useState, useEffect, useContext, useRef } from "react";
import Register from "../Components/Register";
import Login from '../Components/Login';
import ForgotPassword from '../Components/ChangePassword'; 
import { useNavigate } from "react-router-dom";
import { AppProviderStore } from "../AppStore";
import { observer } from "mobx-react";


function Home({passwordReset, login,register}) {
  const { AppStore } = useContext(AppProviderStore);
  const {user} = AppStore
  // const [isGroupView, setGroupView] = useState(false)
  const [showLogin, setShowLogin] = useState(login ? login : false);
  const [showRegister, setShowRegister] = useState(register ? register : false);
  const [showPasswordReset, setShowPasswordReset] = useState(passwordReset)
  const navigate = useNavigate();

  useEffect(()=>{
    if(user){
      if(user.isAdmin && user.email !="<EMAIL>"){
        AppStore.showAdmin = true
        navigate("/admin")
      }else{
        navigate("/account")
      }
    }
  },[user])

  useEffect(() => {
    setShowPasswordReset(passwordReset);
  }, [passwordReset]);

  function handleRegister() {
    setShowLogin(false);
    setShowRegister(true);
  }

  function handleLogin() {
    setShowRegister(false);
    setShowLogin(true);
  }

  return (
    <>
      <div className="app_container p-8">
        <div className="app_loader absolute top-0 left-0 w-full bg-loader h-auto min-h-full bg-cover bg-no-repeat bg-top flex items-center justify-center overflow-y-auto">
          <div className="bg-loaderOpacity h-full flex flex-grow">
            <div className="w-full sm:w-2/3 md:w-1/2 lg:w-1/3 xl:w-1/3 xxl:w-[500px] m-auto h-auto min-h-full flex flex-col justify-center">
              <div className="h-fit shadow-[0_35px_60px_-25px_rgba(255,255,255,0.3)] shadow-xl overflow-hidden rounded-3xl transition-all duration-200 ease-linear hover:scale-[1.05]">
                <div className="bg-violin bg-cover bg-no-repeat p-2 sm:p-5 pb-16 sm:pb-36">
                  <div className="rounded-2xl backdrop-blur-3xl bg-transparent p-4 w-fit">
                    <p className="text-white font-semibold">The Foundation</p>
                    <p className="text-iconGreen font-semibold">for Music Education</p>
                  </div>
                </div>
                <div className="flex flex-col items-center justify-center text-center">
                  {showPasswordReset ? (
                    <ForgotPassword toggleForm={handleLogin} />
                  ) : showLogin ? (
                    <Login toggleForm={handleRegister} />
                  ) : showRegister ? (
                    <Register toggleForm={handleLogin} />
                  ) : (
                    <>
                      <div className="flex flex-row items-center p-4 gap-4 w-full justify-center text-center">
                        <button
                          className="p-5 w-full sm:w-1/2 bg-iconGreen text-white font-semibold rounded-3xl"
                          onClick={handleLogin}
                        >
                          Login
                        </button>
                        <button
                          className="p-5 w-full sm:w-1/2 bg-iconGreen text-white font-semibold rounded-3xl"
                          onClick={handleRegister}
                        >
                          Register
                        </button>
                      </div>
                      <button
                        className="p-3 bg-iconGreen text-white font-semibold rounded-lg"
                        onClick={() => {
                          window.open(`https://www.foundationformusiceducation.org/rules-and-guideline`, '_blank')
                        }}
                      >
                        Rules and Guidelines
                      </button>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );

};

export default observer(Home);
