import { useContext } from "react";
import { useFormContext } from "react-hook-form";
import { AppProviderStore } from "../../../AppStore";
import {
  Typography,
  Box,
  Divider,
  Paper,
  List,
  ListItem,
  ListItemText,
  TextField,
  Button,
  Stack
} from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";

function ReviewStep({ groupNumber, rulesContent, onNavigateToStep }) {
  const { AppStore } = useContext(AppProviderStore);
  const { formOptions } = AppStore;
  const { watch } = useFormContext();

  const formData = watch();
  const groupData = formData[`group_${groupNumber}`];

  // Format the directors list with commas and ampersand
  const formatDirectorsList = (directors) => {
    if (!directors || directors.length === 0) return "No directors provided";

    // Format each director's name
    const formattedNames = directors.map(director =>
      `${director.name.prefix} ${director.name.first} ${director.name.last}`
    );

    // If there's only one director, return just that name
    if (formattedNames.length === 1) return formattedNames[0];

    // If there are two directors, join them with an ampersand
    if (formattedNames.length === 2) return `${formattedNames[0]} & ${formattedNames[1]}`;

    // For more than two directors, use commas and an ampersand before the last one
    const lastDirector = formattedNames.pop();
    return `${formattedNames.join(", ")} & ${lastDirector}`;
  };

  // Format a single soloist's information
  const formatSoloistInfo = (soloist) => {
    if (!soloist || !soloist.name || !soloist.instrument) return null;

    let soloistInfo = `${soloist.name} - ${soloist.instrument}`;
    if (soloist.timeStamp) {
      soloistInfo += ` starting at ${soloist.timeStamp}`;
    }
    return soloistInfo;
  };

  // Check if the optional third track is empty
  const isOptionalTrackEmpty = (track) => {
    if (!track) return true;

    // Check if any of the required fields have data
    const hasTitle = !!track.title;
    const hasComposer = !!track.composer;
    const hasDuration = !!track.duration;
    const hasVenue = !!track.venue;
    const hasFile = !!track.file;

    // If all required fields are empty, consider the track empty
    return !hasTitle && !hasComposer && !hasDuration && !hasVenue && !hasFile;
  };

  // Helper function to get name from ID using formOptions
  const getNameFromId = (type, id) => {
    if (!id) return "Not selected";

    switch (type) {
      case "contest":
        return formOptions.find(option => option._id === id)?.name || id;
      case "category":
        if (!groupData.contest) return id;
        const contest = formOptions.find(option => option._id === groupData.contest);
        return contest?.categories.find(cat => cat._id === id)?.name || id;
      case "classification":
        if (!groupData.contest || !groupData.category) return id;
        const contestObj = formOptions.find(option => option._id === groupData.contest);
        const category = contestObj?.categories.find(cat => cat._id === groupData.category);

        if (Array.isArray(id)) {
          return id.map(classId => {
            return category?.classifications.find(cls => cls._id === classId)?.name || classId;
          }).join(", ");
        }

        return category?.classifications.find(cls => cls._id === id)?.name || id;
      default:
        return id;
    }
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
      <Typography variant="h6" gutterBottom>
        Review Your Information
      </Typography>

      <Typography variant="body1" gutterBottom>
        Please review all the information below before submitting your registration.
      </Typography>

      <Paper elevation={1} sx={{ p: 2, mb: 2, position: 'relative' }}>
        <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 1 }}>
          <Typography variant="subtitle1" fontWeight="bold">
            Director Information
          </Typography>
          <Button
            size="small"
            startIcon={<EditIcon />}
            onClick={() => onNavigateToStep(0)}
            sx={{
              color: '#008544',
              '&:hover': { bgcolor: 'rgba(0, 133, 68, 0.08)' }
            }}
          >
            Edit
          </Button>
        </Stack>
        <List dense>
          <ListItem>
            <ListItemText
              primary="Directors"
              secondary={formatDirectorsList(groupData.directors)}
            />
          </ListItem>
        </List>
      </Paper>

      <Paper elevation={1} sx={{ p: 2, mb: 2, position: 'relative' }}>
        <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 1 }}>
          <Typography variant="subtitle1" fontWeight="bold">
            Ensemble & School Information
          </Typography>
          <Button
            size="small"
            startIcon={<EditIcon />}
            onClick={() => onNavigateToStep(1)}
            sx={{
              color: '#008544',
              '&:hover': { bgcolor: 'rgba(0, 133, 68, 0.08)' }
            }}
          >
            Edit
          </Button>
        </Stack>
        <List dense>
          <ListItem>
            <ListItemText primary="Ensemble Name" secondary={groupData.ensembleName || "Not provided"} />
          </ListItem>
          <ListItem>
            <ListItemText primary="School Name" secondary={groupData.schoolName || "Not provided"} />
          </ListItem>
          <ListItem>
            <ListItemText primary="School Enrollment" secondary={groupData.schoolEnrollment || "Not provided"} />
          </ListItem>
        </List>
      </Paper>

      <Paper elevation={1} sx={{ p: 2, mb: 2, position: 'relative' }}>
        <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 1 }}>
          <Typography variant="subtitle1" fontWeight="bold">
            Contest Details
          </Typography>
          <Button
            size="small"
            startIcon={<EditIcon />}
            onClick={() => onNavigateToStep(2)}
            sx={{
              color: '#008544',
              '&:hover': { bgcolor: 'rgba(0, 133, 68, 0.08)' }
            }}
          >
            Edit
          </Button>
        </Stack>
        <List dense>
          <ListItem>
            <ListItemText
              primary="Contest"
              secondary={getNameFromId("contest", groupData.contest)}
            />
          </ListItem>
          <ListItem>
            <ListItemText
              primary="Category"
              secondary={getNameFromId("category", groupData.category)}
            />
          </ListItem>
          <ListItem>
            <ListItemText
              primary="Classification"
              secondary={getNameFromId("classification", groupData.classification)}
            />
          </ListItem>
          <ListItem>
            <ListItemText
              primary="Registration Fee"
              secondary={`$${groupData.registrationFee || 0}`}
            />
          </ListItem>
        </List>
      </Paper>

      <Paper elevation={1} sx={{ p: 2, mb: 2, position: 'relative' }}>
        <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 1 }}>
          <Typography variant="subtitle1" fontWeight="bold">
            Contact Information
          </Typography>
          <Button
            size="small"
            startIcon={<EditIcon />}
            onClick={() => onNavigateToStep(3)}
            sx={{
              color: '#008544',
              '&:hover': { bgcolor: 'rgba(0, 133, 68, 0.08)' }
            }}
          >
            Edit
          </Button>
        </Stack>
        <List dense>
          <ListItem>
            <ListItemText primary="Cell Phone" secondary={groupData.cellphone || "Not provided"} />
          </ListItem>

          <Divider textAlign="left" sx={{ my: 1 }}>Mailing Address</Divider>

          {groupData.mailingAddress && (
            <>
              <ListItem>
                <ListItemText primary="Street" secondary={groupData.mailingAddress.street || "Not provided"} />
              </ListItem>
              <ListItem>
                <ListItemText primary="City" secondary={groupData.mailingAddress.city || "Not provided"} />
              </ListItem>
              <ListItem>
                <ListItemText primary="State" secondary={groupData.mailingAddress.state || "Not provided"} />
              </ListItem>
              <ListItem>
                <ListItemText primary="ZIP" secondary={groupData.mailingAddress.zip || "Not provided"} />
              </ListItem>
            </>
          )}

          {groupData.shippingAddress && (
            <>
              <Divider textAlign="left" sx={{ my: 1 }}>Shipping Address</Divider>
              <ListItem>
                <ListItemText primary="Street" secondary={groupData.shippingAddress.street || "Not provided"} />
              </ListItem>
              <ListItem>
                <ListItemText primary="City" secondary={groupData.shippingAddress.city || "Not provided"} />
              </ListItem>
              <ListItem>
                <ListItemText primary="State" secondary={groupData.shippingAddress.state || "Not provided"} />
              </ListItem>
              <ListItem>
                <ListItemText primary="ZIP" secondary={groupData.shippingAddress.zip || "Not provided"} />
              </ListItem>
            </>
          )}
        </List>
      </Paper>

      {groupData.tracks && groupData.tracks.length > 0 && (
        <Paper elevation={1} sx={{ p: 2, mb: 2, position: 'relative' }}>
          <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 1 }}>
            <Typography variant="subtitle1" fontWeight="bold">
              Music Selections
            </Typography>
            <Button
              size="small"
              startIcon={<EditIcon />}
              onClick={() => onNavigateToStep(4)}
              sx={{
                color: '#008544',
                '&:hover': { bgcolor: 'rgba(0, 133, 68, 0.08)' }
              }}
            >
              Edit
            </Button>
          </Stack>

          {groupData.tracks.map((track, index) => {
            // Skip the optional third selection if it has no data
            if (index === 2 && isOptionalTrackEmpty(track)) {
              return null;
            }

            return (
              <Box key={index} sx={{ mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Selection #{index + 1}
                </Typography>

                <List dense>
                  <ListItem>
                    <ListItemText primary="Title" secondary={track.title || "Not provided"} />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="Composer/Arranger" secondary={track.composer || "Not provided"} />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="Duration" secondary={track.duration || "Not provided"} />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Date Recorded"
                      secondary={track.dateRecorded ? new Date(track.dateRecorded).toLocaleDateString() : "Not provided"}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="Venue" secondary={track.venue || "Not provided"} />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Audio File"
                      secondary={track.file ? track.file.name : "No file selected"}
                    />
                  </ListItem>

                  {track.soloists && track.soloists.length > 0 && (
                    <>
                      <Divider textAlign="left" sx={{ my: 1 }}>Soloists</Divider>
                      {track.soloists
                        .filter(soloist => soloist && soloist.name && soloist.instrument)
                        .map((soloist, idx) => (
                          <ListItem key={idx}>
                            <ListItemText
                              primary={idx === 0 ? "Soloist" : "Additional Soloist"}
                              secondary={formatSoloistInfo(soloist)}
                            />
                          </ListItem>
                        ))
                      }
                      {track.soloists.filter(soloist => soloist && soloist.name && soloist.instrument).length === 0 && (
                        <ListItem>
                          <ListItemText secondary="No soloists provided" />
                        </ListItem>
                      )}
                    </>
                  )}
                </List>

                {index < groupData.tracks.length - 1 &&
                 // Only show divider if the next track exists and isn't an empty optional track
                 !(index === 1 && groupData.tracks.length > 2 && isOptionalTrackEmpty(groupData.tracks[2])) &&
                 <Divider sx={{ my: 2 }} />}
              </Box>
            );
          })}
        </Paper>
      )}

      <Paper elevation={1} sx={{ p: 2, mb: 2, position: 'relative' }}>
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          Rules and Guidelines
        </Typography>
        <TextField
          multiline
          rows={8}
          value={rulesContent}
          InputProps={{ readOnly: true }}
          fullWidth
          variant="outlined"
          size="small"
        />
      </Paper>

      <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
        By submitting this form, you agree to the rules and guidelines of the Mark of Excellence recorded music competition.
      </Typography>
    </Box>
  );
}

export default ReviewStep;
