import { observer } from 'mobx-react';
import classNames from "classnames";

const AdminSidebar = ({ menuOptions, handleSelect, selectedOption }) => {
  console.log(menuOptions);
  return (
    <div className="w-[270px] pt-10 min-h-full bg-sidebar">
      <ul>
        {menuOptions.map((option, index) => (
          <li
            key={`${index}_${option.title}`}
            onClick={() => handleSelect(option)}
            className={classNames(
              "group flex items-center w-11/12 gap-6 font-semibold p-5 mb-5 duration-300",
              "hover:!bg-[#3b8c6e] hover:!text-white hover:w-[100%]",
              selectedOption?.title === option?.title
                ? "bg-[#3b8c6e] text-white w-[100%]"
                : "bg-white text-grayText"
            )}
          >
            {option.title}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default observer(AdminSidebar);
