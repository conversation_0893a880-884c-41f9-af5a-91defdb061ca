<form>
    <h2>Soloist Notes</h2>
    <textarea/>
    <h2>Performance Notes</h2>
    <textarea/>
    <h2>Group Score</h2>
    <input type="Number" />
</form>

<script>
/**
 * Write out of the data structure plan with Jacob
 * 
 * 6/6/2023
 */

//What are our collections?

//the last thing we have to track is the todo list of a judge
//make sure each judge has stuff to do
//need to be able to get the list of what that judge is responsible for
//and need to make sure we can make a judge responsible for something else

//Judges need to only do one memo per track once
//Use the JudgeID as the key for the memo lists

//assigning judges and creating the links is probably done here
//assigning judges is separate from importing the group data
//assigning judges is dependant on group/track data existing

//probably need an admin screen to move them or see who's done what

var memos = {
    "judgeidnum1324" : { //this is of type memo
            judgeId: judgeId,
            phaseNum: 1, //Is a number so that we can guarantee the ability to sort
            group_score : 100,
            questions: {
                soloist_textarea: "",
                performance_textarea: "",
            },
            audio_memos: [
                "https://whereamI.com/memo1.mp3"
            ]
        },
    "judgeidnum1324" : { //this is of type memo
        judgeId: judgeId,
        phaseNum: 1, //Is a number so that we can guarantee the ability to sort
        group_score : 100,
        questions: {
            soloist_textarea: "",
            performance_textarea: "",
        },
        audio_memos: [
            "https://whereamI.com/memo1.mp3"
        ]
    }
}

//memos are a child of track
var memo = {
    phaseNum: 1, //Is a number so that we can guarantee the ability to sort
    judgeId: judgeId, //which judge made the memo?
    group_score : 100,
    year_or_contest_id: 2023,
    questions: {
        soloist_textarea: "",
        performance_textarea: "",
    },
    audio_memos: [
        "https://whereamI.com/memo1.mp3"
    ], 
}

var group = {
    schoolName: "Patrick Henry Highschool",
    ensembleName: "Junior Brass",
    director: "Mrs. Peters",
    email: "<EMAIL>",
    contest: "Mark of Excellence",
    category: "National Orchestra Honors",
    awards: [

    ],
    classification: [
        {
            "maskedName" : "GroupA",
            "classificationName" : "Highschool Mixed"
        }
    ],
    tracks : {
        "1" : {
            judges: {
                "judge john" : {
                    group_score : 100,
                    questions: {
                        soloist_textarea: "",
                        performance_textarea: "",
                    },
                    audio_memos: [
                        "https://whereamI.com/memo1.mp3"
                    ]
                },
                "judge jacob" : {
                    group_score : 100,
                    questions: {
                        soloist_textarea: "",
                        performance_textarea: "",
                    },
                    audio_memos: [
                        "https://whereamI.com/memo1.mp3"
                    ]
                }
            }

        },
        "2" : {
            questions: {
                soloist_textarea: "",
                performance_textarea: "",
            },
            audio_memos: [
                
            ]
        }
    },
}

var questions = [];