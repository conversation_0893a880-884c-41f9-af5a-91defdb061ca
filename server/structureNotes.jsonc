{
    //Contest Object
    "contest":{
        "name": "String"
    },
    //Category Object
    "category":{
        "name":"String"
    },
    //Classification Object    
    "classification":{
        "name":"String"
    },

    //We create a Competition Object using these.
    //Ex: 
    //Mark of Excellence: National Wind Band Honors - Class A
    //Previously we used all three of the above in multiple locations instead of 
    //referencing an object that has all three already 
    //This should allow for better querying


    //New Competition Object 
    "competition": {
    "contestId": "ObjectId",
    "categoryId": "ObjectId",
    "classificationId": "ObjectId",
    // "entryYear": "Number" -- add year now?
    //For rounds/groups maybe this?
    "rounds": {
      "1": {
        //If two judges in a round they split the group in half: How do
        "groups": [], //array of groupIds
        //do we make unique uri per competition inside judges and make it
        /*
         *[{
            judgeId: String,
            uri: String,
         *}]?
         */
        "judges": [] //array of judgeIds
      }
    }
  },
  //Updated Judge Object
  "judge": {
    "name": "String",
    "email": "String",
    "assignedCompetitions": [
        {
            "id": "String", //competitionId,
            "phase": "String" //[all, 2] 
        }
    ], //array of competition ids
    //handle uris here?:
    "uri": "String",
    //or array of uris if we want to use planned uri structure
    "uris": []
  },
  //Updated Group Object
  "group": {
    "schoolName": "String",
    "ensembleName": "String",
    "director": "String",
    "email": "String",
    //A group can be in two competitions
    "competitions": [
      {
        "id": "String", // competitionId,
        "maskedName": "String", //assigned masked name for this competition
        "scores": [], //array of scores
        "awardEarned": "String" //[National, Commended, null]
      }
    ], //
    "tracks": [] //trackIds
  },
  //Updated tracks
  "track": {
    "title": "String",
    "composer": "String",
    "venue": "String",
    "trackSrc": "String",
    "memos": [] // memoIds
  },
  //New Memo Object
  "memo": {
    "judgeId": "String",
    "audioMemos": [], //array of uris to audio memos
    "notes": "String", //text area response
    "soloistNotes": "String" //text area response
  }
}
