import { z } from 'zod';

// Helper function to create a schema for address
const createAddressSchema = () => z.object({
  street: z.string().min(1, { message: "Street address is required" }),
  city: z.string().min(1, { message: "City is required" }),
  state: z.string().min(1, { message: "State is required" }),
  zip: z.string().min(5, { message: "ZIP code is required" })
});

// Helper function to create a schema for director
const createDirectorSchema = () => z.object({
  name: z.object({
    prefix: z.string().min(1, { message: "Prefix is required" }),
    first: z.string().min(1, { message: "First name is required" }),
    last: z.string().min(1, { message: "Last name is required" })
  })
});

// Helper function to create a schema for soloist
const createSoloistSchema = () => z.object({
  name: z.string().min(1, { message: "Soloist name is required" }),
  instrument: z.string().min(1, { message: "Instrument is required" }),
  timeStamp: z.string().optional()
});

// Helper function to create a schema for track
const createTrackSchema = (isRequired = true) => {
  // Base schema with all fields from the track model
  const baseSchema = {
    title: z.string(),
    composer: z.string(),
    duration: z.string(),
    venue: z.string(),
    dateRecorded: z.any().optional(), // Date can be optional
    soloists: z.array(createSoloistSchema()).optional(),
    file: z.any().optional(), // File is handled separately in the form submission
    minioSrc: z.string().optional(), // This is set after upload
    trackSrc: z.string().optional() // This is set after upload
  };

  if (isRequired) {
    return z.object({
      ...baseSchema,
      // These fields are marked as required in the track model
      title: z.string().min(1, { message: "Title is required" }),
      composer: z.string().min(1, { message: "Composer/Arranger is required" }),
      venue: z.string().min(1, { message: "Recording Event/Venue is required" }),
      // Duration is not marked as required in the model but we'll validate it anyway
      duration: z.string().min(1, { message: "Duration is required" }),
    });
  }

  return z.object(baseSchema);
};

// Create a schema for a group
export const createGroupSchema = () => {
  const baseSchema = {
    directors: z.array(createDirectorSchema()).min(1, { message: "At least one director is required" }),
    ensembleName: z.string().min(1, { message: "Ensemble name is required" }),
    schoolName: z.string().min(1, { message: "School name is required" }),
    schoolEnrollment: z.string().min(1, { message: "School enrollment is required" })
      .or(z.number().positive({ message: "School enrollment must be a positive number" })),
    contest: z.string().min(1, { message: "Contest selection is required" }),
    category: z.string().min(1, { message: "Category selection is required" }),
    classification: z.string().min(1, { message: "Classification selection is required" })
      .or(z.array(z.string()).min(1, { message: "At least one classification is required" })),
    registrationFee: z.number({ message: "Registration fee must be a number" }).nonnegative({ message: "Registration fee must be a non-negative number" }),
    cellphone: z.string().min(1, { message: "Cell phone is required" }),
    mailingAddress: createAddressSchema(),
    shippingAddress: createAddressSchema().optional(),
    // Validate tracks array with proper track schema
    tracks: z.array(createTrackSchema(true)).min(1, { message: "At least one music selection is required" })
  };

  return z.object(baseSchema);
};

// Create a schema for the entire form
export const createFormSchema = (numberOfGroups) => {
  const schema = {};

  for (let i = 0; i < numberOfGroups; i++) {
    schema[`group_${i}`] = createGroupSchema();
  }

  return z.object(schema);
};
