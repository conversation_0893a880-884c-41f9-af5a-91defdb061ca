import { BsCheckCircle } from "react-icons/bs";
import classNames from "classnames";
import { useState, useContext, Fragment, useEffect } from "react";
import { observer } from "mobx-react";
import { AppProviderStore } from "./../../AppStore";
import { toJS } from "mobx";

const AppSidebar = ({ handleSelect, selectedGroup, classifications }) => {
  const { AppStore } = useContext(AppProviderStore);

  const [isExpanded, setExpanded] = useState({});
  const [groupList, setGroupList] = useState([]);

  function toggleSublist(index) {
    /**
     *was thinking of doing a sorting by score by default on toggle. 
     *not used at the moment and should be handled by the store instead of right here. 
      setGroupList(
      [...AppStore.classifications[index].groups].sort((a, b) => {
        if (a.score === null && b.score === null) {
          return 0; // Maintain the default order for groups without a score
        }
        if (a.score === null) {
          return -1; // Place groups without a score at the top
        }
        if (b.score === null) {
          return 1; // Place groups without a score at the top
        }
        return b.score - a.score; // Sort other groups by score in descending order
      })
    );
     */

    setExpanded((prevState) => ({
      ...prevState,
      [index]: !prevState[index],
    }));
    console.log(isExpanded);
  }
  return (
    <div className="w-[270px] pt-10 min-h-full bg-sidebar sidebar">
      {AppStore.classifications.map((classification, index) => (
        <>
          {classification.groups.length ? (
            <ul>
              <li
                key={classification._id}
                onClick={() => toggleSublist(index)}
                className={classNames(
                  "group flex items-center w-11/12 gap-6 font-semibold p-5  mb-5 duration-300",
                  " hover:!bg-[#3b8c6e] hover:!text-white hover:w-[100%]",
                  "bg-white text-grayText"
                )}
              >
                {classification.name}
              </li>
              {isExpanded[index] && (
                <ul>
                  {classification.groups.map((group, index) => {
                    const hasScore =
                      group.score ||
                      (group?.classifications?.find(
                        (obj) => obj && obj.id === classification._id
                      ) || {}).score;
                    return (
                      <li
                        key={group._id}
                        className={classNames(
                          "group flex items-center w-11/12 gap-2 font-semibold p-5 pr-0 mb-5 duration-300",
                          " hover:!bg-[#3b8c6e] hover:!text-white hover:w-[100%]",
                          selectedGroup?._id === group._id
                            ? "bg-sidebarListItem text-white w-[100%]"
                            : "bg-white text-grayText"
                        )}
                        onClick={() => {
                          handleSelect(group);
                          AppStore.setEnsemble(group);
                        }}
                      >
                        {hasScore && (
                          <BsCheckCircle
                            color="#008544"
                            size={26}
                            className={classNames(
                              selectedGroup?._id === group._id && "text-white",
                              "group-hover:text-white"
                            )}
                          />
                        )}
                        <p
                          className={classNames(
                            selectedGroup?._id === group._id
                              ? "text-white"
                              : "text-sidebarListItem",
                            "group-hover:text-white w-8 flex items-center justify-center h-8 text-[10px] rounded-full"
                          )}
                        >
                          {hasScore ? group.score : ""}
                        </p>
                        Group:{" "}
                        {group.maskedName
                          ? group.maskedName
                          : (group.classifications.find(
                              (obj) => obj && obj.id === classification._id
                            ) || {}).maskedName}
                      </li>
                    );
                  })}
                </ul>
              )}
            </ul>
          ) : null}
        </>
      ))}
    </div>
  );
  
  return (
    <div className="w-[270px] pt-10 min-h-full bg-sidebar sidebar">
      {AppStore.classifications.map((classification, index) => (
        <>
          {classification.groups.length ? (
            <ul>
              <li
                key={classification._id}
                onClick={() => toggleSublist(index)}
                className={classNames(
                  "group flex items-center w-11/12 gap-6 font-semibold p-5  mb-5 duration-300",
                  " hover:!bg-[#3b8c6e] hover:!text-white hover:w-[100%]",
                  "bg-white text-grayText"
                )}
              >
                {classification.name}
              </li>
              {isExpanded[index] && (
                <ul>
                  {classification.groups.map((group, index) => (
                    <li
                      key={group._id}
                      className={classNames(
                        "group flex items-center w-11/12 gap-2 font-semibold p-5 pr-0 mb-5 duration-300",
                        " hover:!bg-[#3b8c6e] hover:!text-white hover:w-[100%]",
                        selectedGroup?._id === group._id
                          ? "bg-sidebarListItem text-white w-[100%]"
                          : "bg-white text-grayText"
                      )}
                      onClick={() => {
                        handleSelect(group);
                        AppStore.setEnsemble(group);
                      }}
                    >
                      {group.score ? (
                        <BsCheckCircle
                          color="#008544"
                          size={26}
                          className={classNames(
                            selectedGroup?._id === group._id && "text-white",
                            "group-hover:text-white"
                          )}
                        />
                      ) : (
                        <Fragment />
                      )}
                      <p
                        className={classNames(
                          selectedGroup?._id === group._id
                            ? "text-white"
                            : "text-sidebarListItem",
                          "group-hover:text-white w-8 flex items-center justify-center h-8 text-[10px] rounded-full"
                        )}
                      >
                        {/* change line below according to logic */}
                        {group.score}
                      </p>
                      Group: {group.maskedName ? group.maskedName : group.classifications.find(obj=>obj.id === classification._id).maskedName}
                    </li>
                  ))}
                </ul>
              )}
            </ul>
          ) : null}
        </>
      ))}
    </div>
  );
};
export default observer(AppSidebar);
