@tailwind base;
@tailwind components;
@tailwind utilities;

.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}
/* Add this CSS to your stylesheet or inside a style tag */
@media (max-width: 768px) {
  
  .print-button {
      display: none;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

.sidebar {
  height: 100%;
  overflow-y: auto;
}

.sidebar{
  overflow-y:  'scroll';
  scrollbar-color:  #dddddd #dddddd !important;
  scrollbar-width: thin !important;
}
.sidebar::-webkit-scrollbar {
  width: 10px;
  margin-right: 2px;
  margin: 3px;
}

.sidebar::-webkit-scrollbar-track {
  box-shadow: inset 0 0 6px #dddddd;
  margin-right: 3px;
  background-color: #0d3d75;
}

.sidebar::-webkit-scrollbar-thumb {
  background-color: #ffffff;
  outline: none;
  border-radius: 5px;
}
.main_content::-webkit-scrollbar {
  width: 10px;
  margin-right: 2px;
  margin: 3px;
  background-color: #ffffff;
}

.main_content::-webkit-scrollbar-track {
  box-shadow: inset 0 0 6px #dddddd;
  height: 200px;
  background-color: #ffffff;
}

.main_content::-webkit-scrollbar-thumb {
  background-color: #0d3d75;
  outline: none;
  border-radius: 5px;
  height: 150px;
}
@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.result-container {
  display: block;
  width: 100%;
  height: 100%;
}

.clearfix {
  width: 80%;
  max-width: 1200px;
  margin: 0 auto;
}

.contest-title {
  font-weight: bold;
  text-align: center;
  font-size: 2em;
}

.awardType {
  font-weight: bold;
  margin-top: 20px;
  font-size: 1.5em;
}

/* Flexbox styles */

.flex-row {
  display: flex;
  justify-content: space-between;
  padding: 5px 0;  /* Some padding for each row */
}

.flex-cell {
  flex: 1;  /* Each cell will take equal width by default */
  padding: 5px;  /* Optional: to give some spacing inside cells */
}

.flex-cell.ensembleName {
  flex: 2;  /* This will make ensembleName take twice the width of the other cells */
}

.flex-cell.director {
  text-align: center;
}

.flex-cell.location {
  text-align: right;
}
.print-container {
  display: none;
}

@media print {
  .clearfix {
    width: 95%; /* Reduced width to 90% to avoid touching the edge */
    max-width: none; /* Removing the max-width constraint */
    margin: 5% auto; /* Introducing margins on top and bottom for some spacing */
    /* page-break-after: always; Forces a new page after this element */
  }

  .contest-title, .awardType, .flex-cell {
    font-size: 1.2em;
  }

  .print-container {
    display: block;
  }
  .flex-row {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 5px 0;
  }
  
  .flex-cell {
    padding: 2px;
    white-space: nowrap;
  }
  
  .flex-cell.ensembleName {
    flex: 3 1 50%; /* Grow faster, and start with half the available space */
    text-align: left;
  }
  
  .flex-cell.director {
    flex: 1 1 20%; /* Allow some growth, start with 20% of the available space */
    text-align: left;
  }
  
  .flex-cell.location {
    flex: 1 1 20%; /* Similar to director */
    text-align: right;
  }

  .awardType {
    margin-top: 10px;
    page-break-inside: avoid; /* Prevents page breaks inside the element, keeping content together */
  }
}
.active-tab-class {
  background-color: #3B8C6E;
  color: white;
  border: none;
  padding: 10px 20px;
}

.inactive-tab-class {
  background-color: #E9ECEF;
  color: #495057;
  border: none;
  padding: 10px 20px;
}
