import { observer } from 'mobx-react';
function Groups({ groupItem, index }) {
    console.log(groupItem)
    return (
        <div className='p-4 w-full border border-dullGray grid grid-cols-[1fr_1fr_1fr_1fr_1fr_1fr_1fr_1fr] items-center gap-4 w-full hover:bg-dullGray' key={index}>
            <div className="flex items-center font-normal text-black text-sm">{groupItem.category}</div>
            <div className="flex items-center font-normal text-black text-sm">{groupItem.classification}</div>
            <div className="flex items-center font-normal text-black text-sm">{groupItem.contest}</div>
            <div className="flex items-center font-normal text-black text-sm">{groupItem.director}</div>
            <div className="flex items-center font-normal text-black text-sm">{groupItem.ensembleName}</div>
            <div className="flex items-center font-normal text-black text-sm">{groupItem.maskedName}</div>
            <div className="flex items-center font-normal text-black text-sm">{groupItem.schoolName}</div>
            {groupItem.tracks?.length > 0 && <div className='flex items-center gap-4 flex-wrap'>
                {groupItem.tracks &&
                    groupItem.tracks.map((track, counter) => {
                        return <span key={counter} className=" py-2 px-4 shadow-md no-underline rounded-full bg-green text-white font-normal text-xs btn-primary hover:text-white hover:bg-iconGreen focus:outline-none active:shadow-none mr-2">{track.title}</span>
                    })}
            </div>}
        </div>
    );
}

export default observer(Groups);
