import React from 'react';
import {
  Box,
  Typography,
  Paper,
  LinearProgress,
  Chip
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Assignment as AssignmentIcon,
  Class as ClassIcon
} from '@mui/icons-material';

const OverallProgressCard = ({ overallProgress }) => {
  return (
    <Paper sx={{ p: 3, mb: 4, borderRadius: 2, boxShadow: 2 }}>
      <Typography variant="h6" sx={{ mb: 2, color: '#3b8c6e', display: 'flex', alignItems: 'center' }}>
        <AssignmentIcon sx={{ mr: 1 }} /> Overall Judging Progress
      </Typography>

      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 2 }}>
        <Chip
          icon={<ClassIcon />}
          label={`${overallProgress.total} Total Classifications`}
          size="medium"
          sx={{ borderColor: '#3b8c6e', color: '#3b8c6e', fontWeight: 'bold' }}
          variant="outlined"
        />
        <Chip
          icon={<CheckCircleIcon />}
          label={`${overallProgress.completed} Completed`}
          size="medium"
          sx={{ borderColor: '#3b8c6e', color: '#3b8c6e', fontWeight: 'bold' }}
          variant="outlined"
        />
        <Chip
          icon={<AssignmentIcon />}
          label={`${overallProgress.remaining} Remaining`}
          size="medium"
          sx={{
            borderColor: overallProgress.remaining > 0 ? '#ff9800' : '#3b8c6e',
            color: overallProgress.remaining > 0 ? '#ff9800' : '#3b8c6e',
            fontWeight: 'bold'
          }}
          variant="outlined"
        />
      </Box>

      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <Typography variant="body1" sx={{ mr: 2, minWidth: '120px', fontWeight: 'bold' }}>
          Total Progress:
        </Typography>
        <Box sx={{ flexGrow: 1 }}>
          <LinearProgress
            variant="determinate"
            value={overallProgress.progress}
            sx={{
              height: 10,
              borderRadius: 5,
              backgroundColor: 'rgba(59, 140, 110, 0.2)',
              '& .MuiLinearProgress-bar': {
                backgroundColor: '#3b8c6e',
              }
            }}
          />
        </Box>
        <Typography variant="body1" sx={{ ml: 2, fontWeight: 'bold' }}>
          {Math.round(overallProgress.progress)}%
        </Typography>
      </Box>
    </Paper>
  );
};

export default OverallProgressCard;
