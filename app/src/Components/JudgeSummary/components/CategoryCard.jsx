import React from 'react';
import {
  Box,
  Typo<PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ontent,
  Divider,
  Chip,
  LinearProgress,
  List
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Assignment as AssignmentIcon,
  Category as CategoryIcon,
  Class as ClassIcon
} from '@mui/icons-material';
import ClassificationItem from './ClassificationItem';

const CategoryCard = ({ category, onClassificationClick }) => {
  return (
    <Card sx={{ mb: 3, boxShadow: 3 }}>
      <CardHeader
        title={
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <CategoryIcon sx={{ mr: 1, color: '#3b8c6e' }} />
            <Typography variant="h6">{category.name}</Typography>
          </Box>
        }
        subheader={
          <Box sx={{ mt: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
              <Chip
                icon={<ClassIcon />}
                label={`${category.totalClassifications} Classifications`}
                size="small"
                variant="outlined"
                sx={{ borderColor: '#3b8c6e', color: '#3b8c6e' }}
              />
              <Chip
                icon={<CheckCircleIcon />}
                label={`${category.completedClassifications} Completed`}
                size="small"
                variant="outlined"
                sx={{ borderColor: '#3b8c6e', color: '#3b8c6e' }}
              />
              <Chip
                icon={<AssignmentIcon />}
                label={`${category.remainingClassifications} Remaining`}
                size="small"
                variant="outlined"
                sx={{ borderColor: category.remainingClassifications > 0 ? '#ff9800' : '#3b8c6e', color: category.remainingClassifications > 0 ? '#ff9800' : '#3b8c6e' }}
              />
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Typography variant="body2" sx={{ mr: 1, minWidth: '100px' }}>
                Classification Progress:
              </Typography>
              <Box sx={{ flexGrow: 1 }}>
                <LinearProgress
                  variant="determinate"
                  value={category.progress}
                  sx={{
                    height: 8,
                    borderRadius: 5,
                    backgroundColor: 'rgba(59, 140, 110, 0.2)',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: '#3b8c6e',
                    }
                  }}
                />
              </Box>
              <Typography variant="body2" sx={{ ml: 1, fontWeight: 'bold' }}>
                {Math.round(category.progress)}%
              </Typography>
            </Box>
          </Box>
        }
      />
      <Divider />
      <CardContent>
        <Typography variant="subtitle1" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
          <ClassIcon sx={{ mr: 1, color: '#3b8c6e' }} />
          Classifications
        </Typography>

        <List>
          {category.classifications.map((classification) => (
            <ClassificationItem 
              key={classification.id}
              classification={classification}
              categoryId={category.id}
              onClick={onClassificationClick}
            />
          ))}
        </List>
      </CardContent>
    </Card>
  );
};

export default CategoryCard;
