import Category from "../models/category.mjs";
import Judge from "../models/judge.mjs";
import Group from "../models/group.mjs";
import * as Minio from 'minio';
import { v4 as uuidv4 } from 'uuid';
import Papa from 'papaparse';

// mongoose.set('debug', true);


var minioClient = new Minio.Client({
  endPoint: 'app.foundationformusiceducation.org',
  port: 443,
  useSSL: true,
  accessKey: 'fme-app-1',
  secretKey: 'D0fqEd5GCgBdGRhKiwNgAwbXRdkqIwRM1BtMmqWQ',
});

export default async function (fastify, opts) {

  async function sortGroups(groups, id) {
    console.log('sortingGroups')
    console.log(groups);
    const getMaskedName = (group) => {
      if (group.maskedName) {
        return group.maskedName;
      } else if (group.classifications) {
        const classification = group.classifications.find(
          (c) => c.id.toString() === id.toString()
        );
        if (classification && classification.maskedName) {
          return classification.maskedName;
        }
      }
      return null;
    };


    return groups.sort((a, b) => {
      const maskedNameA = getMaskedName(a);
      const maskedNameB = getMaskedName(b);

      if (!maskedNameA && !maskedNameB) {
        return 0;
      } else if (!maskedNameA) {
        return 1;
      } else if (!maskedNameB) {
        return -1;
      } else {
        const lengthDiff = maskedNameA.length - maskedNameB.length;
        if (lengthDiff === 0) {
          return maskedNameA.localeCompare(maskedNameB);
        } else {
          return lengthDiff;
        }
      }
    });
  }
  // fastify.post('/api/minioUpload', async function (req,res){
  //   console.log('minioUpload')
  //   console.log(req.body)
  //   res.send('hit route')
  //   // minioClient.putObject('fme-app', fileName, file, (err,objInfo)=>{
  //   //   if(err){
  //   //     res.status(500).send(err)
  //   //     return console.log(err)
  //   //   }
  //   //   console.log('success', objInfo.etag,objInfo.versionId)
  //   //   res.status(200).send(objInfo)
  //   // })
  // })
  fastify.post('/api/minioPresignedUrl', async function (req, res) {
    console.log('presigned url route')
    try {
      const filename = req.body.filename;
      console.log(filename);

      const url = await new Promise((resolve, reject) => {
        minioClient.presignedPutObject('fme-app', filename, (err, url) => {
          if (err) reject(err);
          else resolve(url);
        });
      });

      console.log(url);
      //let newUrl = url.replace(/192\.168\.30\.49:9000/, 'app.foundationformusiceducation.org')
      res.send({ presigned: url });
    } catch (error) {
      console.log(error)
      res.status(500).send({ error: error.message });
    }
  });

  fastify.post('/api/removeMinioObject', async function (req, res) {
    let filename = req.body.filename
    try {
      await minioClient.removeObject('fme-app', filename)
      res.send(200)
    } catch (error) {
      console.error(error)
      res.send(500)
    }
  })

  fastify.get('/api/presignedUrl', async function (req, res) {
    let fileType = req.query.filetype;
    let year = new Date().getFullYear()
    let filename = `/${year}/judges/judge-intros/${uuidv4()}.${fileType}`
    minioClient.presignedPutObject('fme-app', filename, (err, url) => {
      if (err) throw err
      console.log('presigned request')
      console.log(url)
      res.send({ presigned: url, filename: filename })
    })
  })

  fastify.post('/api/upload', async function (req, res) {
    const maxRetries = 5;
    let fileType = req.query.filetype;
    console.log('uploading a ', fileType);
    const file = await req.file();
    let url = '';
    let retryCount = 0;

    while (retryCount < maxRetries) {
      try {
        await new Promise((resolve, reject) => {
          let filename = `${uuidv4()}.${fileType}`;
          minioClient.putObject("fme-app", filename, file.file, function (error, etag) {
            console.log('in mino')
            if (error) {
              console.log('error')
              console.log(error)
              retryCount++;
              console.log('retry #', retryCount)
              if (retryCount === maxRetries) {
                res.send(error);
              }
              // Retry uploading by resolving the promise with an error
              resolve(error);
            } else {
              console.log('upload succeeded on try #', retryCount)
              url = `https://s3.yobo.dev/fme-app/${filename}`;
              resolve();
            }
          });
        });

        // Break the loop if upload is successful
        if (url !== '') {
          break;
        }
      } catch (error) {
        console.log('upload error on try #', retryCount)
        console.log(error)
        retryCount++;
        if (retryCount === maxRetries) {
          res.send(error);
        }
      }
    }

    res.send(url);
  });

  fastify.get('/api/getWinners', async function (req, res) {
    let categoryId = req.query?.categoryId;
    let year = req.query?.year

    let data = await Category.findById(categoryId).select({ 'name': 1, 'classifications': 1, '-_id': 0 }).populate('classifications');

    let category = data.name
    // Sort classifications alphabetically
    let classifications = data.classifications.sort((a, b) => a.name.localeCompare(b.name));
    function assignAward(array) {
      //console.log('assignAward', array)
      if (array.isCommended) return "Commended"
      if (array.isCitation) return "Citation"
      if (array.isNational) return "National"
      if (array.isState) return "State"
      else return false
    }
    let awards = {
      "Citation": [],
      "National": [],
      "Commended": [],
      "State": []
    };

    let statesMap = {
      'Alabama': 'AL', 'Alaska': 'AK', 'Arizona': 'AZ', 'Arkansas': 'AR',
      'California': 'CA', 'Colorado': 'CO', 'Connecticut': 'CT', 'Delaware': 'DE',
      'Florida': 'FL', 'Georgia': 'GA', 'Hawaii': 'HI', 'Idaho': 'ID',
      'Illinois': 'IL', 'Indiana': 'IN', 'Iowa': 'IA', 'Kansas': 'KS',
      'Kentucky': 'KY', 'Louisiana': 'LA', 'Maine': 'ME', 'Maryland': 'MD',
      'Massachusetts': 'MA', 'Michigan': 'MI', 'Minnesota': 'MN', 'Mississippi': 'MS',
      'Missouri': 'MO', 'Montana': 'MT', 'Nebraska': 'NE', 'Nevada': 'NV',
      'New Hampshire': 'NH', 'New Jersey': 'NJ', 'New Mexico': 'NM', 'New York': 'NY',
      'North Carolina': 'NC', 'North Dakota': 'ND', 'Ohio': 'OH', 'Oklahoma': 'OK',
      'Oregon': 'OR', 'Pennsylvania': 'PA', 'Rhode Island': 'RI', 'South Carolina': 'SC',
      'South Dakota': 'SD', 'Tennessee': 'TN', 'Texas': 'TX', 'Utah': 'UT',
      'Vermont': 'VT', 'Virginia': 'VA', 'Washington': 'WA', 'West Virginia': 'WV',
      'Wisconsin': 'WI', 'Wyoming': 'WY'
    }

    const replacements = {
      'Performing and Visual Arts Middle School': 'PVA MS',
      'Junior High School': 'JHS',
      'Junior High': 'JH',
      'High School': 'HS',
      'Middle School': 'MS'
    };

    for (const classification of classifications) {
      let filter
      if (year === "2023") {
        filter = { category: categoryId, classification: classification._id, award: { $exists: true } }
        //If Category ID is National Wind Band Honors
        //console.log(categoryId,'643456dbdd98890aad8cef31' )
        if (categoryId === '643456dbdd98890aad8cef31') {
          //console.log('national wind band')
          filter = {
            year: year,
            category: categoryId,
            $or: [
              // For single classification
              {
                classification: classification._id,
                award: { $exists: true }
              },
              // For multiple classifications
              {
                classifications: {
                  $elemMatch: {
                    id: classification._id,
                    award: { $exists: true }
                  }
                }
              }
            ]
          };
        }
      } else {
        //If Category ID is National Wind Band Honors
        if (categoryId === "643456dbdd98890aad8cef31") {
          //console.log('wind band')
          filter = {
            year: 2024,
            category: categoryId,
            $and: [
              {
                $or: [
                  { classification: classification._id },
                  {
                    classifications: {
                      $elemMatch: {
                        id: classification._id,
                      }
                    }
                  }
                ]
              },
              {
                $or: [
                  { "adminOverrides.isCitation": true },
                  { "adminOverrides.isNational": true },
                  { "adminOverrides.isCommended": true },
                  { "adminOverrides.isState": true }
                ]
              }
            ]
          }

          //console.log('filtering', filter)
        } else {
          //console.log('non wind bad')
          filter = {
            year: year, category: categoryId, classification: classification._id, $or: [
              { "adminOverrides.isCitation": true },
              { "adminOverrides.isNational": true },
              { "adminOverrides.isCommended": true },
              { "adminOverrides.isState": true }
            ]
          }
        }

      }
      //console.log('fitler:', filter)
      let winnerList = await Group.find(filter).populate("tracks").lean();

      let classificationWinners = {};

      for (const winner of winnerList) {
        //console.log('looping through winners')
        let awardValue;

        if (winner.classifications && winner.classifications.length) {
          let matchingClassification = winner.classifications.find(c => c.id.toString() === classification._id.toString())
          let matchingClassificationIndex = winner.classifications.findIndex(c => c.id.toString() === classification._id.toString());
          console.log('current classification: ', classification)
          console.log('winner:', winner._id)
          console.log('winner classifications', winner.classifications)
          console.log('winner overrides', winner.adminOverrides)
          console.log('matched', matchingClassification)
          if (year === "2023") {
            awardValue = matchingClassification.award
          } else {
            awardValue = assignAward(winner.adminOverrides[matchingClassificationIndex])
            if (!awardValue) {
              continue;
            }
          }
        } else {
          //console.log('single classification')
          if (year === "2024") {
            //console.log('using adminOverride')
            awardValue = assignAward(winner.adminOverrides[0])
            //console.log(awardValue)
          } else {
            awardValue = winner.award
          }
        }
        if (winner.director.includes(' and ')) {
          winner.director = winner.director.replace(/\band\b/g, '&');
        }
        let address = winner.address ? winner.address : winner.mailingAddress
        if (address === undefined) {
          address = {
            street: 'Missing',
            city: 'Missing',
            state: 'NA',
            zip: "Missing"
          }
        }
        if (address.state.length > 2) {
          address.state = statesMap[address.state];
        }

        if (!classificationWinners[awardValue]) {
          //console.log('setting awardValue array', awardValue)
          classificationWinners[awardValue] = [];
        }
        //console.log('classificationWinners', classificationWinners )
        function adjustEnsembleName(ensembleName, schoolName) {
          const abbreviations = {
            "Junior High School": "JHS",
            "Junior High": "JH",
            "Middle School": "MS",
            "High School": "HS"
          };

          const lowerEnsembleName = ensembleName.toLowerCase();
          let lowerSchoolName = schoolName.toLowerCase();

          if (lowerEnsembleName.includes(lowerSchoolName)) {
            console.log('ensemble name includes school name');
            console.log(ensembleName, schoolName);
            return ensembleName; // Full schoolName already exists, return as-is.
          }

          for (const [full, abbrev] of Object.entries(abbreviations)) {
            const lowerFull = full.toLowerCase();

            if (schoolName === 'Miami Arts Studio 6-12 @ Zelda Glazer') {
              return ensembleName;
            }

            if (lowerSchoolName.includes('intermediate')) {
              return ensembleName;
            }

            if (lowerSchoolName.includes(lowerFull)) {
              const partialName = schoolName.replace(new RegExp(` ${full}`, 'i'), '');
              if (ensembleName.toLowerCase().startsWith(partialName.toLowerCase()) && !lowerEnsembleName.includes(lowerFull)) {
                return ensembleName.replace(new RegExp(partialName, 'i'), schoolName);
              }
            }
          }

          lowerSchoolName = schoolName.replace(/band/i, '').trim();
          if (lowerSchoolName === "stratford high school, spring branch isd".toLowerCase()) {
            return ensembleName;
          }

          return `${schoolName} ${ensembleName}`; // If no matches found, prepend schoolName       
        }

        let ensembleName = winner.ensembleName;
        ensembleName = adjustEnsembleName(ensembleName, winner.schoolName);

        for (const [key, value] of Object.entries(replacements)) {
          ensembleName = ensembleName.replace(new RegExp(key, 'g'), value);
        }

        let city = address.city

        if (!/^([A-Z][a-z]+)( [A-Z][a-z]+)*$/.test(city)) {
          city = city.toLowerCase().split(' ').map(word => {
            if (word.startsWith('mc')) {
              return 'Mc' + word.charAt(2).toUpperCase() + word.slice(3);
            } else if (word.startsWith("o'")) {
              return "O'" + word.charAt(2).toUpperCase() + word.slice(3);
            }
            return word.charAt(0).toUpperCase() + word.slice(1);
          }).join(' ');
        }
        //console.log('im here before push', awardValue)
        //console.log(classificationWinners)
        classificationWinners[awardValue].push({
          groupId: winner._id,
          schoolName: winner.schoolName,
          ensembleName: ensembleName,
          director: winner.director,
          email: winner.email,
          street: address.street,
          city: city,
          state: address.state,
          zip: address.zip,
          tracks: winner.tracks
        });
      }
      // Sort winners for each award type based on ensembleName
      for (const winnersForAward of Object.values(classificationWinners)) {
        winnersForAward.sort((a, b) => a.ensembleName.localeCompare(b.ensembleName));
      }

      for (const [awardType, winnersForAward] of Object.entries(classificationWinners)) {
        let classificationName = classification.name
        switch (true) {
          case category.includes('Band'):
            if (classificationName.includes('New')) {
              break;
            } else {
              classificationName = `Band Class ${classificationName}`
              break;
            }
          case category.includes('Orchestra'):
            classificationName += ' Orchestra'
            break;
          case category.includes('Jazz'):
            classificationName += ' Jazz Ensemble'
            break;
          case category.includes('Percussion'):
            classificationName += ' Percussion Ensemble'
            break;
          case category.includes('Choral'):
            if (classificationName.includes("Open Class")) {
              break;
            }
            classificationName += ' Choir'
            break;
          default:
            break;
        }

        awards[awardType].push({
          classification: classificationName,
          winners: winnersForAward
        });
      }
    }

    if (req.query.format === 'csv') {
      const flatData = [];
      for (const [awardType, classifications] of Object.entries(awards)) {
        classifications.forEach(classificationData => {
          classificationData.winners.forEach(winner => {

            // Prepare the track details for CSV
            let trackDetails = [];
            winner.tracks.forEach(track => {
              trackDetails.push(track.title);
              trackDetails.push(track.composer);
              trackDetails.push(track.minioSrc);
            });

            let groupRow = {
              award: awardType,
              classification: classificationData.classification,
              groupId: winner.groupId,
              schoolName: winner.schoolName,
              ensembleName: winner.ensembleName,
              director: winner.director,
              email: winner.email,
              street: winner.street,
              city: winner.city,
              state: winner.state,
              zip: winner.zip
            };

            // Appending track details to the row
            winner.tracks.forEach((track, index) => {
              groupRow[`title_${index + 1}`] = track.title;
              groupRow[`composer_${index + 1}`] = track.composer;
              groupRow[`link_${index + 1}`] = track.minioSrc;
            });

            flatData.push(groupRow);
          });
        });
      }

      const csv = Papa.unparse(flatData);

      res.header('Content-Type', 'text/csv');
      res.header('Content-Disposition', `attachment; filename=${data.name}-winners.csv`);
      res.send(csv);
    } else {
      res.send(awards);
    }

    if (req.query.format === 'html') {


    }
  });

  fastify.get("/api/wpToken", async function (request, reply) {
    let wpUrl = 'https://foundationformusiceducation.org/wp-json/jwt-auth/v1/token'
    console.log(request)
    let credentials = {
      username: request.query.username,
      password: request.query.password
    }
    console.log(credentials)
    try {
      const response = await fetch(wpUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials)
      })
      console.log(response)
      if (!response.ok) {
        throw new Error('Failed to authenticate')
      }

      const data = await response.json();
      return data.token;
    } catch (error) {
      console.error('Error fetching the token: ', error)
    }
  })
  //temp route till we create judges in db
  //as judges will have all the information needed
  fastify.get("/api/setListByURL", async function (request, reply) {
    try {
      if (request.query?.groupId) {
        let groupId = request.query.groupId
        let group = await Group.findById(groupId).populate({ path: "tracks" }).populate({ path: "classification", select: "name" }).populate({ path: "classifications.id", select: "name" })
        console.log(group)
        let categoryId = group.category
        let classifications;
        console.log('hmm', group.classification)
        if (group.classifications.length) {
          console.log('group.classification:', group.classifications)

          classifications = group.classifications.map(classification => classification.id)
          console.log('classifications', classifications)
        } else {
          console.log('group.classification:', group.classification)
          classifications = [group.classification]
          console.log('classification', classifications)
        }
        let judgeIntros = []
        console.log(classifications)
        for (let classification of classifications) {
          console.log('bacon', classification)
          let classificationId = classification._id
          const intros = await Judge.aggregate([
            {
              $match: {
                $or: [
                  {
                    $and: [
                      { categories: categoryId },
                      { classifications: classification._id },
                    ]
                  },
                  {
                    $and: [
                      { "assigned.category": categoryId },
                      { "assigned.classifications.id": classification._id },
                    ]
                  }
                ]
              },
            },
            {
              $facet: {
                withAssigned: [
                  {
                    $match: {
                      "assigned": { $exists: true, $type: "array" }
                    }
                  },
                  { $unwind: "$assigned" },
                  { $unwind: "$assigned.classifications" },
                  {
                    $project: {
                      _id: 1,
                      name: 1,
                      judgeIntro: 1,
                      validClassification: {
                        $or: [
                          {
                            $and: [
                              { $eq: ["$categories", categoryId] },
                              { $eq: ["$classifications", classification._id] }
                            ]
                          },
                          {
                            $and: [
                              { $eq: ["$assigned.classifications.id", classification._id] },
                              {
                                $cond: [
                                  { $isArray: "$assigned.classifications.groups" },
                                  { $in: [groupId, "$assigned.classifications.groups"] },
                                  true
                                ]
                              }
                            ]
                          }
                        ]
                      }
                    }
                  }
                ],
                withoutAssigned: [
                  {
                    $match: {
                      "assigned": { $exists: false },
                      categories: categoryId,
                      classifications: classification._id
                    }
                  },
                  {
                    $project: {
                      _id: 1,
                      name: 1,
                      judgeIntro: 1,
                      validClassification: {
                        $literal: true
                      }
                    }
                  }
                ]
              }
            },
            {
              $project: {
                combined: { $concatArrays: ["$withAssigned", "$withoutAssigned"] }
              }
            },
            { $unwind: "$combined" },
            {
              $replaceRoot: {
                newRoot: "$combined"
              }
            },
            {
              $match: {
                validClassification: true
              }
            },
            {
              $group: {
                _id: "$_id",
                name: { $first: "$name" },
                judgeIntro: { $first: "$judgeIntro" }
              }
            }
          ]);
          console.log('intros', intros)
          judgeIntros.push({ classification: classification.name, intros: [...intros] })
        }

        let returnData = {
          group: group,
          judgeIntros: judgeIntros
        }

        return returnData
      } else {
        let categoryId = request.query.categoryId;
        let judgeId = request.query.judgeId;

        let categoryData = await Category.findById(categoryId)
          .populate({
            path: "classifications",
            select: "name",
          })
          .select("-__v");

        if (!categoryData) {
          reply.code(404).send({ error: "Category not found" });
          return;
        }

        let judgeData = await Judge.findById(judgeId);
        if (!judgeData) {
          reply.code(404).send({ error: "Judge not found" });
          return;
        }
        let classifications = [];
        if (judgeData?.assigned && judgeData.assigned.length) {
          console.log('I have judge.assigned')
          console.log(judgeData.assigned)
          judgeData.assigned.map(assignment => {
            console.log('assignment category - url category')
            console.log(assignment.category.toString() === categoryId)
          })
          console.log(categoryId)
          let categoryAssignment = judgeData.assigned.find(obj => obj.category.toString() === categoryId)
          console.log('categoryAssignment')
          console.log(categoryAssignment)
          for (const classification of categoryAssignment.classifications) {

            let pushObj = {
              _id: classification.id.toString(),
              name: categoryData.classifications.find(obj => obj._id.toString() === classification.id.toString()).name
            }
            if (classification?.phase) {
              pushObj.phase = classification.phase
            }
            if (classification?.groups && classification.groups.length) {

              let groups = await Group.find({
                _id: {
                  $in: classification.groups
                }
              }).populate({ path: "tracks", select: "-__v" }).lean();
              groups = await sortGroups(groups, pushObj._id)
              pushObj.groups = groups

            } else {

              let query = {
                category: categoryId,
                $or: [
                  { classification: classification.id },
                  { classifications: { $elemMatch: { id: classification.id.toString() } } }
                ]
              }
              if (classification.phase === 2) {
                //need a way to get only phase2 groups.
                console.log('phase2 groups')
                //let groups = await Group.find().populate({ path: "tracks", select: "-__v" })
              }
              let groups = await Group.find(query).populate({ path: "tracks", select: "-__v" }).lean()

              groups = await sortGroups(groups, pushObj._id)
              //Temp to hide phase 2 for demo
              if (classification.phase === 2) {
                groups = []
              }
              pushObj.groups = groups

            }

            classifications.push(pushObj)
          }
        } else {
          for (const classification of categoryData.classifications) {
            let pushObj = {
              _id: classification._id,
              name: classification.name,
            };
            let groups = await Group.find({
              category: categoryId,
              $or: [
                { classification: classification._id },
                { classifications: { $elemMatch: { id: classification._id } } }
              ]
            }).populate({ path: "tracks", select: "-__v" });
            groups = await sortGroups(groups, pushObj._id)
            pushObj.groups = groups;
            classifications.push(pushObj);
          }
        }


        let returnData = {
          categoryId: categoryId,
          category: categoryData.name,
          classifications: classifications,
          judge: judgeData,  // replaced with judgeData
        };
        return returnData;
      }

    } catch (error) {
      console.error(error);
      reply.code(500).send({ error: 'Internal server error' });
    }
  });
  //Current implementation to get all groups associated with the provided email from the form submission
  fastify.get("/api/getByEmail", async function (request, reply) {
    let email = request.query.email
    let data = await Group.find({ email: { $regex: new RegExp("^" + email + "$", "i") } }).populate({ path: "tracks" }).populate({ path: "classification", select: "name" }).populate({ path: "classifications.id", select: "name" }).lean()
    let responseData = []
    if (data.length === 0) {
      reply.code(401)
    }
    for (const group of data) {
      console.log('in group loop', group)
      let groupId = group._id
      let categoryId = group.category
      let classifications;
      console.log('hmm', group.classification, group.classifications)
      if (group.classifications && group.classifications.length) {
        console.log('group.classification:', group.classifications)

        classifications = group.classifications.map(classification => classification.id)
        console.log('classifications', classifications)
      } else {
        console.log('group.classification:', group.classification)
        classifications = [group.classification]
        console.log('classification', classifications)
      }
      let judgeIntros = []
      console.log(classifications)
      for (let classification of classifications) {
        console.log('bacon', classification)
        let classificationId = classification._id
        const intros = await Judge.aggregate([
          {
            $match: {
              $or: [
                {
                  $and: [
                    { categories: categoryId },
                    { classifications: classification._id },
                  ]
                },
                {
                  $and: [
                    { "assigned.category": categoryId },
                    { "assigned.classifications.id": classification._id },
                  ]
                }
              ]
            },
          },
          {
            $facet: {
              withAssigned: [
                {
                  $match: {
                    "assigned": { $exists: true, $type: "array" }
                  }
                },
                { $unwind: "$assigned" },
                { $unwind: "$assigned.classifications" },
                {
                  $project: {
                    _id: 1,
                    name: 1,
                    judgeIntro: 1,
                    validClassification: {
                      $or: [
                        {
                          $and: [
                            { $eq: ["$categories", categoryId] },
                            { $eq: ["$classifications", classification._id] }
                          ]
                        },
                        {
                          $and: [
                            { $eq: ["$assigned.classifications.id", classification._id] },
                            {
                              $cond: [
                                { $isArray: "$assigned.classifications.groups" },
                                { $in: [groupId, "$assigned.classifications.groups"] },
                                true
                              ]
                            }
                          ]
                        }
                      ]
                    }
                  }
                }
              ],
              withoutAssigned: [
                {
                  $match: {
                    "assigned": { $exists: false },
                    categories: categoryId,
                    classifications: classification._id
                  }
                },
                {
                  $project: {
                    _id: 1,
                    name: 1,
                    judgeIntro: 1,
                    validClassification: {
                      $literal: true
                    }
                  }
                }
              ]
            }
          },
          {
            $project: {
              combined: { $concatArrays: ["$withAssigned", "$withoutAssigned"] }
            }
          },
          { $unwind: "$combined" },
          {
            $replaceRoot: {
              newRoot: "$combined"
            }
          },
          {
            $match: {
              validClassification: true
            }
          },
          {
            $group: {
              _id: "$_id",
              name: { $first: "$name" },
              judgeIntro: { $first: "$judgeIntro" }
            }
          }
        ]);
        console.log('intros', intros)
        judgeIntros.push({ classification: classification.name, intros: [...intros] })
      }

      let toPush = {
        group: group,
        judgeIntros: judgeIntros
      }
      responseData.push(toPush)
    }

    reply.send(responseData)
  })
  fastify.get("/api/getSoloists", async function (req, res) {
    let categoryId = req.query?.categoryId;
    let year = req.query?.year;
    let category = await Category.findById(categoryId);
    let fileName = `${category.name.replace(/ /g, "_")}-soloists.csv`;

    let data = await Group.find({ category: categoryId, year: year })
      .select({
        schoolName: 1,
        ensembleName: 1,
        classification: 1,
        classifications: 1,
      })
      .populate({
        path: 'category',
        select: 'name'
      })
      .populate({
        path: 'classification',
        select: 'name'
      })
      .populate({
        path: 'classifications',
        select: 'name'
      })
      .populate('tracks')
      .lean();

    let outStandingSoloists = [];

    function splitContent(content) {
      // Remove text within parentheses and the parentheses themselves
      let cleanedContent = content.replace(/\(.*?\)/g, '')
        .replace(/!\s+/g, ', ')
        .replace(/!+/g, '')
        .replace(/[()]/g, '');

      // Split by commas, slashes, or newlines, and trim whitespace from each part
      return cleanedContent
        .toLowerCase()
        .split(/,\s*|\/\s*|\n\s*/)
        .map(part => part.replace(/ - .*/, '').trim()) // Remove descriptors after " - "
        .filter(part => part !== ''); // Remove empty strings
    }

    function findPartialMatch(contentArr, soloists) {
      console.log(contentArr);
      // Function to check if any part of contentArr matches an instrument partially
      let matchedPartialContent = contentArr.filter(content =>
        soloists.some(soloist => {
          console.log('findPartialMatch');
          console.log(soloist.instrument.toLowerCase());
          console.log(content);
          return soloist.instrument.toLowerCase().includes(content);
        })
      );
      console.log('found partial matches:', matchedPartialContent);
      return matchedPartialContent;
    }

    data.forEach(group => {
      group.tracks.forEach(track => {
        if (!track.soloists || track.soloists.length === 0) {
          return;
        }

        if (!track.memos || !track.memos.soloistNotes || track.memos.soloistNotes.length === 0) {
          return;
        }

        const hasContent = track.memos.soloistNotes.some(note => note.content && note.content.trim() !== "");
        if (!hasContent) {
          return;
        }

        let trackSoloists = track.soloists.map(soloist => soloist.instrument.toLowerCase());

        track.memos.soloistNotes.forEach(notes => {
          let content = notes.content.toLowerCase();
          let contentArr = splitContent(content); // Now uses the updated splitContent
          let remainingContentArr = [...contentArr];
          console.log('Starting Arr', contentArr);
          console.log('Available Instruments', trackSoloists);

          track.soloists.forEach(soloist => {
            let instrument = soloist.instrument.toLowerCase();
            console.log('current instrument', instrument);

            let index = remainingContentArr.indexOf(instrument);
            console.log('Remaining Arr', remainingContentArr);

            if (index !== -1) {
              console.log('match found', instrument, remainingContentArr[index]);
              remainingContentArr.splice(index, 1);
              outStandingSoloists.push({
                groupId: group._id,
                trackId: track._id,
                schoolName: group.schoolName,
                ensembleName: group.ensembleName,
                soloist: soloist.name,
                instrument: soloist.instrument,
                content: notes.content
              });
            } else {
              console.log('No full match for:', instrument);
            }
          });

          console.log('Checking for Partials');
          console.log(remainingContentArr.length, remainingContentArr);
          if (remainingContentArr.length > 0) {
            let matchedPartialContent = findPartialMatch(remainingContentArr, track.soloists);
            matchedPartialContent.forEach(content => {
              remainingContentArr = remainingContentArr.filter(item => item !== content); // Remove matched content
              track.soloists.forEach(soloist => {
                let instrument = soloist.instrument.toLowerCase();
                console.log('current partial check:', instrument);
                if (instrument.includes(content)) {
                  console.log('partial match found:', instrument, content);
                  outStandingSoloists.push({
                    groupId: group._id,
                    trackId: track._id,
                    schoolName: group.schoolName,
                    ensembleName: group.ensembleName,
                    soloist: soloist.name,
                    instrument: soloist.instrument,
                    content: notes.content
                  });
                } else {
                  console.log('No partial match for:', instrument, content);
                }
              });
            });
          }
        });
      });
    });

    // Convert to CSV using PapaParse
    const csv = Papa.unparse(outStandingSoloists);

    res.header('Content-Type', 'text/csv');
    res.header('Content-Disposition', `attachment; filename=${fileName}`);
    res.code(200).send(csv);
  });



  fastify.get("/api/heartbeat", { logLevel: 'silent' }, async function (req, res) {
    const serverSelect = req.headers['x-server-select'];
    if (serverSelect === 'dev') {
      res.code(200).send('dev');
    } else {
      res.code(200).send('prod');
    }
  })

}
