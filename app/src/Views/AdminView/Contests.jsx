import { observer } from 'mobx-react';
import { useState } from 'react';
import axios from 'axios';
import Swal from 'sweetalert2';
import EditIcon from '@mui/icons-material/Edit';
import CheckIcon from '@mui/icons-material/Check';
import CloseIcon from '@mui/icons-material/Close';

function Contests({ contest, index }) {
    console.log(contest);
    const [price, setPrice] = useState(contest.price);
    const [originalPrice, setOriginalPrice] = useState(contest.price); // To store the original price
    const [isEditing, setIsEditing] = useState(false); // To toggle between view and edit modes

    // Function to update the price when submitting the update
    const updatePrice = async () => {
        if (price !== contest.price) {
            try {
                console.log('Sending update request for price:', price);
                const response = await axios.patch(`/api/contests/${contest._id}/price`, {
                    price,
                });
                Swal.fire('Price updated successfully!', '', 'success');
                setOriginalPrice(price); // Update the original price after successful update
                setIsEditing(false); // Exit edit mode
            } catch (error) {
                Swal.fire('Error updating price', error.message, 'error');
                console.error('Update failed:', error);
            }
        }
    };

    return (
        <div className='p-4 w-full border border-dullGray grid grid-cols-[1fr_3fr_1fr] items-center gap-4 hover:bg-dullGray' key={index}>
            <div className="flex items-center font-normal text-black text-sm">{contest.name}</div>
            {contest.categories?.length > 0 && (
                <div className='flex items-center gap-4 flex-wrap'>
                    {contest.categories.map((category, counter) => (
                        <span key={counter} className="py-2 px-4 shadow-md no-underline rounded-full bg-green text-white font-sans font-semibold text-sm border-blue btn-primary hover:text-white hover:bg-iconGreen focus:outline-none active:shadow-none mr-2">
                            {category.name}
                        </span>
                    ))}
                </div>
            )}
            <div className="flex items-center gap-2">
                {isEditing ? (
                    <>
                        <input
                            value={price}
                            onChange={(e) => setPrice(e.target.value)} // Update the price state on change
                            className="font-semibold text-black text-sm border p-2 rounded"
                            min="0"
                        />
                        <div className="flex gap-2">
                            <CloseIcon 
                                className="cursor-pointer"
                                style={{ fontSize: '20px', color: 'red' }} // Set the color to red using inline styling
                                onClick={() => {
                                    setPrice(originalPrice); // Reset to original price
                                    setIsEditing(false); // Exit edit mode
                                }}
                            />
                            <CheckIcon 
                                className="cursor-pointer"
                                style={{ fontSize: '20px', color: '#3B8C6E' }} // Set the color to #3B8C6E (green) using inline styling
                                onClick={updatePrice} // Submit the updated price
                            />
                        </div>
                    </>
                ) : (
                    <>
                        <span className="font-semibold text-black text-sm">{price}</span>
                        <EditIcon 
                            className="cursor-pointer"
                            style={{ fontSize: '20px', color: '#032741' }} // Set color to #032741 using inline styling
                            onClick={() => setIsEditing(true)} // Enable edit mode when pencil is clicked
                        />
                    </>
                )}
            </div>
        </div>
    );
}

export default observer(Contests);
