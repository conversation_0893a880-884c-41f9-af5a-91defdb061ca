import * as React from 'react';
import { useState } from 'react';
import { Drawer, Divider, List, ListItem, ListItemButton, ListItemIcon, ListItemText } from '@mui/material'
import { SettingsIcon, GavelIcon, AccountCircleIon, LeaderboardIcon, AccountBalanceIcon, DownloadIcon, EmojiEventsIcon } from '@mui/icons-material'


import { observer } from 'mobx-react';
const links = [
    { text: 'Home', href: '/', icon: AccountCircleIcon },
    { text: 'Leaderboard', href: '/leaderboard/', icon: LeaderboardIcon },
    { text: 'Recommended Events', href: '/events/', icon: EventAvailableIcon },
    { text: 'Find Events', href: '/events/find/', icon: EventIcon },
    // { text: 'Score Card', href: '/scorecard', icon: SportsScoreIcon },
    // { text: 'Create User', href: '/user/create', icon: AddIcon },
];
const SECONDLINKSTIER = [
    { text: 'Carolinas Leaderboard', href: '/leaderboard/', icon: LeaderboardIcon },
    { text: 'Carolinas Schedule', href: '/events/find/', icon: EventIcon },
];
const PLACEHOLDER_LINKS = [
    // { text: 'Profile Switcher', href: '/admin/profiles', icon: LogoutIcon },
    { text: 'Coach Profile', href: '/user/parent/profile', icon: SettingsIcon },
    { text: 'Association Profile', href: '/user/parent/profile', icon: SettingsIcon },
    { text: 'Golfer Profile', href: '/user/golfer/profile', icon: SettingsIcon },
    { text: 'Parent Profile', href: '/user/parent/profile', icon: SettingsIcon },
    { text: 'Login', href: '/user/login', icon: LogoutIcon },
    { text: 'Debug', href: '/debug', icon: DebugIcon }
];


const Sidebar = () => {
    const EXPANDED_DRAWER_WIDTH = 240;
    const CLOSED_DRAWER_WIDTH = 56;
    const [DRAWER_WIDTH, setDrawerWidth] = useState(56);
    const MainStore = React.useContext(MainStoreContext)
    // TODO: where is data grid getting that background color from??
    return (
        <Drawer
            onMouseEnter={() => {
                setDrawerWidth(EXPANDED_DRAWER_WIDTH);
            }}
            onMouseLeave={() => {
                setDrawerWidth(CLOSED_DRAWER_WIDTH);
            }}
            sx={{
                display: 'block',
                width: EXPANDED_DRAWER_WIDTH,
                flexShrink: 0,
                '& .MuiDrawer-paper': {
                    width: { md: DRAWER_WIDTH, sm: MainStore.drawer ? '70%' : 0, xs: MainStore.drawer ? '70%' : 0 },
                    boxSizing: 'border-box',
                    top: ['0px', '0px', '0px'],
                    height: 'auto',
                    paddingTop: '100px',
                    bottom: 0,
                    // overflow: 'hidden',
                    // overflowY: { md: 'none', sm: 'auto', sx: 'auto' },
                    transition: 'all .2s linear',
                    whiteSpace: 'nowrap'
                },
                '*::-webkit-scrollbar': {
                    width: '0.4em'
                },
                '*::-webkit-scrollbar-track': {
                    '-webkit-box-shadow': 'inset 0 0 6px rgba(0,0,0,0.00)'
                },
                '*::-webkit-scrollbar-thumb': {
                    backgroundColor: 'rgba(0,0,0,.1)',
                    outline: '1px solid slategrey'
                }
            }}
            variant={"permanent"}
            anchor="left"
        >
            <List>
                {LINKS.map(({ text, href, icon: Icon }) => (
                    <ListItem key={href} disablePadding onClick={() => MainStore.toggleDrawer(MainStore.drawer ? false : true)}>
                        <ListItemButton component={Link} href={href}>
                            <ListItemIcon>
                                <Icon />
                            </ListItemIcon>
                            <ListItemText primary={text} />
                        </ListItemButton>
                    </ListItem>
                ))}
            </List>
            <Divider sx={{ mt: 'auto' }} />
            <List>
                {SECONDLINKSTIER.map(({ text, href, icon: Icon }) => (
                    <ListItem key={href} disablePadding onClick={() => MainStore.toggleDrawer(MainStore.drawer ? false : true)}>
                        <ListItemButton component={Link} href={href}>
                            <ListItemIcon>
                                <Icon />
                            </ListItemIcon>
                            <ListItemText primary={text} />
                        </ListItemButton>
                    </ListItem>
                ))}
            </List>
            <Divider sx={{ mt: 'auto' }} />
            <List>
                {PLACEHOLDER_LINKS.map(({ text, href, icon: Icon }) => (
                    <ListItem key={text} disablePadding >
                        <ListItemButton component={Link} href={href}>
                            <ListItemIcon>
                                <Icon />
                            </ListItemIcon>
                            <ListItemText primary={text} />
                        </ListItemButton>
                    </ListItem>
                ))}
            </List>
        </Drawer>
    )
}

export default observer(Sidebar);