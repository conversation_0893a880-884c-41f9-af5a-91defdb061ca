const { google } = require('googleapis')
const credentials = require('./credentials.json')
const scopes = ["https://www.googleapis.com/auth/drive"]
const auth = new google.auth.JWT(credentials.client_email, null, credentials.private_key, scopes)
const drive = google.drive({ version: "v3", auth })
const docs = google.docs({ version: "v1", auth })
const redis = require('redis')
const { content } = require('googleapis/build/src/apis/content')
const mongoose = require('mongoose')


const fastify = require('fastify')({
    logger: true
})

const redisClient = redis.createClient({
    url: 'redis://redis:6379'
})

let categoryRedis
let responseRedis

fastify.register(require('@fastify/cors', (instance) => {
    return (req, callback) => {
        const corsOptions = {
            origin: true
        }
        if (/^localhost$/m.test(req.headers.origin)) {
            corsOptions.origin = false
        }

        // callback expects two parameters: error and options
        callback(null, corsOptions)
    }
}))


async function fetchById(id) {
    //gets by id and returns name.
    //remove fields to return all data
    try {
        let file = await drive.files.get({
            fileId: id,
            fields: 'name'
        })
        return (file.data.name)
    } catch (err) {
        console.log(error)
    }
}
async function fetchByParent(id) {
    try {
        let res = await drive.files.list({
            q: `'${id}' in parents`
        })
        return res.data.files
    } catch (error) {
        console.log(error)
    }
}

async function createFile(fileMetadata) {
    try {
        const file = await drive.files.create({
            resource: fileMetadata,
            fields: 'id'
        })
        return file.data.id
    } catch (err) {
        console.error(err)
    }
}

fastify.get('/api/category', async function (request, reply) {

    let parentId = request.query.parentId
    let judge = request.query.judge
    let event = await fetchById(parentId)

    categoryRedis = await redisClient.get('category')
    if (!categoryRedis) {
        redisClient.set('category', parentId)
    }

    responseRedis = await redisClient.get('response')
    if (responseRedis) {
        reply.send(JSON.parse(responseRedis))
    }

    if (categoryRedis !== parentId || !responseRedis) {
        let files = await fetchByParent(parentId)
        let groupClass = await generateLists(files, "class")
        let data = {
            judge: judge,
            event: event,
            groups: groupClass
        }
        // console.log('sending this', data)
        reply.send(data)
        //Caching for now though this wont work for multiple judges
        redisClient.set('response', JSON.stringify(data))
    }

})

fastify.get('/api/groups', async function (request, reply) {
    // console.log('/api/groups')
    //we can chain requests but I feel like there might be a better way to do this? 
    //though I couldnt find anything at the moment.
    let parentId = request.query.parentId
    try {
        const res = await drive.files.list({
            q: `'${parentId}' in parents`,
        })
        let groupList = await generateLists(res.data.files, "groups")
        // console.log('grouplist?')
        console.dir(groupList, { depth: null })
    } catch (err) {
        console.log(err)
    }
})

async function generateLists(files, type, currentList) {
    let list = currentList ? [...currentList] : []
    for (const file of files) {
        if (type === 'groups') {
            let group = {
                name: file.name,
                driveId: file.id,
                trackList: await getTracks(file.id)
            }
            list.push(group)
        } else if (type === 'tracks') {
            let track = {
                title: file.name,
                driveId: file.id,
                trackSrc: await getSrc(file.id),
                memos: await getMemos(file.id)
            }
            console.log(track.memos)
            list.push(track)
        } else if (type === 'class') {
            // console.log(file)
            let eventClass = {
                name: file.name,
                driveId: file.id,
                groups: await getGroups(file.id)
            }
            console.log(eventClass)
            list.push(eventClass)
        }

    }
    return list
}

async function getGroups(classId) {
    let groupFolders = await fetchByParent(classId)
    let groupList = await generateLists(groupFolders, 'groups')
    return groupList

}

async function getTracks(groupId) {
    try {
        const res = await drive.files.list({
            q: `'${groupId}' in parents`,
            name: "Tracks"
        })
        const trackFolder = await fetchByParent(res.data.files[0].id)
        let trackList = await generateLists(trackFolder, 'tracks')
        return trackList
    } catch (err) {
        console.log(err)
    }
}

async function getSrc(trackId) {
    let trackFolder = await fetchByParent(trackId)

    //grab the audio file and set src id
    let track = trackFolder.find(file => file.mimeType.includes("audio"))
    let trackSrc = track.id

    //checking for shared permissions
    let res = await drive.permissions.list({
        fileId: trackId
    })
    let hasPermission = res.data.permissions.some(permission => permission.type === "anyone")
    if (!hasPermission) {
        await drive.permissions.create({
            fileId: track.id,
            requestBody: {
                role: 'reader',
                type: 'anyone'
            }
        })
    }

    return trackSrc
}

async function getMemos(trackId) {
    let trackFolder = await fetchByParent(trackId)
    let data = {
        audioMemos: [],
        notes: []
    }
    //check if we have memos folder
    let memoFolder = trackFolder.find(files => files.name === "Memos")
    //if no memos folder create one
    if (!memoFolder) {
        const fileMetadata = {
            name: 'Memos',
            mimeType: 'application/vnd.google-apps.folder',
            parents: [trackId]
        };

        data.driveId = await createFile(fileMetadata)

    } else {
        data.driveId = memoFolder.id
        let memoFiles = await fetchByParent(memoFolder.id)

        //checking if memos exist
        //noteIds -- currenty findOne though should probably make this a filter
        let filterNotes = memoFiles.filter(file => file.mimeType === 'application/vnd.google-apps.document')
        let noteIds = filterNotes.length > 0 && filterNotes.map(note => note.id)
        let filterAudio = memoFiles.filter(file => file.mimeType.includes("audio"))
        let audioMemos = filterAudio.length > 0 && filterAudio.map(audio => audio.id)
        if (!noteIds && !audioMemos) {
            console.log(data)
            return data
        } else {
            if (noteIds) {
                console.log(noteIds)
                for (const noteId of noteIds) {
                    let document = await docs.documents.get({
                        documentId: noteId
                    })
                    let note = {
                        title: document.data.title,
                        content: ''
                    }
                    let contentArray = document.data.body.content
                    for (const content of contentArray) {
                        if (content?.paragraph) {
                            note.content += ' ' + content.paragraph.elements[0].textRun.content
                        }
                    }
                    data.notes.push(note)
                }
            
            }
            if (audioMemos) {
                data.audioMemos = audioMemos
            }
        }

    }
    return data
}
fastify.get('/api/folders', async function (request, reply) {
    try {
        const res = await drive.files.list({
            q: "mimeType = 'application/vnd.google-apps.folder'",
        })
        reply.send(res.data.files)
    } catch (err) {
        console.log(err)
    }
})

const start = async () => {
    try {
        redisClient.on('error', (err) => console.log('redis client error', err))
        await redisClient.connect();
        await fastify.listen({ host: '0.0.0.0', port: 3000 })
    } catch (err) {
        fastify.log.error(err)
        process.exit(1)
    }
    await mongoose.connect("mongodb://mongo:27017/fme")
    console.log(mongoose.connection.readyState)
}
start()
