import React, { useState, useContext } from 'react';
import { useStripe, useElements, PaymentElement } from '@stripe/react-stripe-js';
import { Button, CircularProgress, Typography } from '@mui/material';
import { AppProviderStore } from "../../../AppStore";
import Swal from 'sweetalert2';

const CheckoutForm = ({ amount, invoiceNo, clientSecret, handleClose }) => {
  const stripe = useStripe();
  const elements = useElements();
  const { AppStore } = useContext(AppProviderStore);
  const { selectedGroup, updateSelectedGroup } = AppStore;
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);

  const handleSubmit = async (event) => {
    event.preventDefault();

    if (!stripe || !elements || !clientSecret) return;

    setLoading(true);
    setErrorMessage(null);

    try {
      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: window.location.href,
        },
        redirect: 'if_required',
      });

      if (error) {
        if (error.code === 'payment_intent_expired') {
          setErrorMessage('Payment session expired. Please refresh the page to try again.');
          window.location.reload();
          return;
        }
        setErrorMessage(error.message);
      } else if (paymentIntent && paymentIntent.status === 'succeeded') {
        // Create a URL to the Stripe dashboard for this payment
        const paymentId = paymentIntent.id;
        const stripePaymentUrl = `https://dashboard.stripe.com/payments/${paymentId}`;

        // Update the selectedGroup's invoicePaid to true and set checkNo to the payment URL
        const groupUpdate = {
          filter: { _id: selectedGroup._id },
          toUpdate: {
            invoicePaid: true,
            checkNo: stripePaymentUrl
          },
        };

        const updated = await updateSelectedGroup(groupUpdate);
        if (updated) {
          // Close the modal first
          handleClose();

          // Show success message
          Swal.fire({
            icon: 'success',
            title: 'Payment Successful!',
            text: `Your payment of $${amount.toFixed(2)} for invoice #${invoiceNo} has been processed successfully.`,
            confirmButtonColor: '#008544'
          });
        } else {
          throw new Error('Failed to update group status');
        }
      } else {
        setErrorMessage('Payment processing, please wait...');
      }
    } catch (err) {
      setErrorMessage('An unexpected error occurred. Please try again.');
      Swal.fire({
        icon: 'error',
        title: 'Payment Error',
        text: 'There was an error processing your payment. Please try again.',
        confirmButtonColor: '#008544'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="checkout-form">
      <Typography variant="h5" gutterBottom>
        Enter Payment Details
      </Typography>

      {!clientSecret ? (
        <Typography variant="body2" color="error">Loading payment details...</Typography>
      ) : (
        <PaymentElement />
      )}

      {errorMessage && <Typography color="error" variant="body2">{errorMessage}</Typography>}

      <div className="payment-summary">
        <Typography variant="h6">Amount: ${amount.toFixed(2)}</Typography>
      </div>

      <Button
        onClick={handleSubmit}
        disabled={loading || !stripe || !elements || !clientSecret}
        variant="contained"
        color="primary"
        fullWidth
        sx={{ backgroundColor: '#008544', '&:hover': { backgroundColor: '#006f32' } }}
      >
        {loading ? <CircularProgress size={24} /> : 'Pay Now'}
      </Button>
    </div>
  );
};

export default CheckoutForm;
