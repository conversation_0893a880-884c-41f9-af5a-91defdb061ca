version: "3"
services:
  react:
    image: node:18
    restart: always
    networks:
      - fme-app
    working_dir: /home/<USER>/client
    command: npm start
    volumes:
      - ./app:/home/<USER>/client
    ports:
      - 32023:3000
    expose:
      - "3000"
    depends_on:
      - server
    environment:
      REACT_APP_ENV: dev
      REACT_APP_STRIPE_PUBLIC_KEY: pk_test_51NaOMMIGzc7LgHDW487hAhbqh6v0N981dJC2K0mZZHzh69uR3GEHnkHKUwimldvdbXldCWQL9PslCiZuINWJEIL600dvmnErV9
      ##REACT_APP_STRIPE_PUBLIC_KEY: pk_live_51R7NbAGx7aUR4VHWhIE16wuVOVtx6sVTGzzVgIJdkEhfABA8BRRhkSuNofbXqi9lIO5L44XdyRTzrUDnhjDXcHb400lvQx4JNL
  redis:
    image: redis:latest
    restart: always
    expose:
      - '6379'
    networks:
      - fme-app
    depends_on:
      - server
  mongo:
    image: mongo:6.0
    restart: always
    networks:
      - fme-app
    ports: 
      - 21288:27017
    volumes: 
      - ./data/db/dumps/mongo_dumps:/tmp/dump
      - ./data/db/mongo_data:/data/db
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: root
      ME_CONFIG_MONGODB_ADMINPASSWORD: root
  server:
    image: node:18
    restart: always
    networks:
      - fme-app
    working_dir: /home/<USER>/server
    command: npm start
    volumes:
      - ./server:/home/<USER>/server
    environment:
      NODE_ENV: dev
      SENDGRID_API_KEY: *********************************************************************
      STRIPE_SK_TEST: sk_test_51NaOMMIGzc7LgHDWEdMWrSy0l7FiHOfBWpioTyBcd55eZlmRYV9hEA3ZgTcJhveqapmRcJeNrMBGx5chabAsYaEQ00rcEkAjy0
      STRIPE_SK_LIVE: ***********************************************************************************************************
    ports:
      - 3001:3000
    depends_on:
      - mongo
    expose:
      - "3000"
networks:
  fme-app: