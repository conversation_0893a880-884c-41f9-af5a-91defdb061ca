import mongoose from "mongoose";
const { Schema, model } = mongoose;

const judgeScoreSchema = new Schema({
  judgeId: {
    type: Schema.Types.ObjectId,
    ref: "Judges"
  },
  score: Number,
  movesOn: Boolean,
  isCommended: Boolean,
  isNational: Boolean,
  isCitation: Boolean,
  isState: Boolean
})

const groupSchema = new Schema({
  userId: {
    type: Schema.Types.ObjectId,
    ref: "User"
  },
  year: {
    type: Number,
    default: new Date().getFullYear()
  },
  adminOverrides: [{
    movesOn: {
      type: Boolean,
      default: false
    },
    isCommended: {
      type: Boolean,
      default: false
    },
    isNational: {
      type: Boolean,
      default: false
    },
    isCitation: {
      type: Boolean,
      default: false
    },
    isState: {
      type: Boolean,
      default: false
    }
  }],
  schoolName: {
    type: String,
    required: true,
  },
  schoolEnrollment: {
    type: Number
  },
  ensembleName: {
    type: String,
    required: true,
  },
  maskedName: {
    type: String,
  },
  director: {
    type: String,
    required: true,
  },
  email: {
    type: String,
    required: true,
  },
  cellphone: {
    type: String
  },
  mailingAddress: {
    street: String,
    city: String,
    state: String,
    zip: String
  },
  shippingAddress: {
    street: String,
    city: String,
    state: String,
    zip: String
  },
  contest: {
    type: Schema.Types.ObjectId,
    ref: "Contests",
  },
  category: {
    type: Schema.Types.ObjectId,
    ref: "Categories",
  },
  classification: {
    type: Schema.Types.ObjectId,
    ref: "Classifications",
  },
  classifications: {
    type: [{
      id: {
        type: Schema.Types.ObjectId,
        ref: "Classifications"
      },
      maskedName: String,
      judgeScores: {
        type: [judgeScoreSchema],
        default: []
      },
      score: Number, //ignore this score
      downloadCode: String,
      _id: false
    }]
  },
  tracks: [
    {
      type: Schema.Types.ObjectId,
      ref: "Tracks",
    },
  ],
  judgeScores: {
    type: [judgeScoreSchema],
    default: []
  },
  score: Number,
  downloadCode: String,
  awards: [{
    type: String
  }],
  registrationFee: {
    type: Number
  },
  invoice: {
    type: Schema.Types.ObjectId,
    ref: "Invoice"
  },
  invoiceSent: {
    type: Boolean,
  },
  invoicePaid: {
    type: Boolean,
    default: false
  },
  scoresReceived: {
    type: Boolean,
    default: false
  },
  checkNo: String,
  isArchived: {
    type: Boolean,
    default: false
  }
});

groupSchema.pre('save', async function (next) {
  const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const base = alphabet.length;

  // Function to generate masked name based on count
  const generateMaskedName = (count) => {
    let maskedName = '';
    do {
      maskedName = alphabet[count % base] + maskedName;
      count = Math.floor(count / base) - 1;
    } while (count >= 0);
    return maskedName;
  };

  if (this.classifications && this.classifications.length > 0) {
    // If classifications are present
    delete this.judgeScores
    for (const classification of this.classifications) {
      const existingGroupsCount = await this.constructor.countDocuments({
        category: this.category,
        'classifications.id': classification.id,
        year: this.year
      });

      classification.maskedName = generateMaskedName(existingGroupsCount);
    }
  } else {
    // If no classifications, calculate maskedName for the main group
    const existingGroupsCount = await this.constructor.countDocuments({
      category: this.category,
      classification: this.classification,
      year: this.year
    });

    this.maskedName = generateMaskedName(existingGroupsCount);
  }

  next();
});

const Group = model("Groups", groupSchema);
export default Group;
