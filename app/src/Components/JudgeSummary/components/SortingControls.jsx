import React from 'react';
import { Box, Button } from '@mui/material';
import { Sort as SortIcon } from '@mui/icons-material';

const SortingControls = ({ sortConfig, requestSort, showMovesOnSort = false }) => {
  return (
    <Box sx={{ display: 'flex', gap: 2 }}>
      <Button
        variant={sortConfig.key === 'maskedName' ? 'contained' : 'outlined'}
        color="primary"
        size="small"
        onClick={() => requestSort('maskedName')}
        startIcon={sortConfig.key === 'maskedName' && <SortIcon />}
        endIcon={sortConfig.key === 'maskedName' && (
          sortConfig.direction === 'asc' ? '↑' : '↓'
        )}
      >
        Sort by Name
      </Button>

      <Button
        variant={sortConfig.key === 'score' ? 'contained' : 'outlined'}
        color="primary"
        size="small"
        onClick={() => requestSort('score')}
        startIcon={sortConfig.key === 'score' && <SortIcon />}
        endIcon={sortConfig.key === 'score' && (
          sortConfig.direction === 'asc' ? '↑' : '↓'
        )}
      >
        Sort by Score
      </Button>

      <Button
        variant={sortConfig.key === 'hasScore' ? 'contained' : 'outlined'}
        color="primary"
        size="small"
        onClick={() => requestSort('hasScore')}
        startIcon={sortConfig.key === 'hasScore' && <SortIcon />}
        endIcon={sortConfig.key === 'hasScore' && (
          sortConfig.direction === 'asc' ? '↑' : '↓'
        )}
      >
        Sort by Status
      </Button>

      {showMovesOnSort && (
        <Button
          variant={sortConfig.key === 'movesOn' ? 'contained' : 'outlined'}
          color="primary"
          size="small"
          onClick={() => requestSort('movesOn')}
          startIcon={sortConfig.key === 'movesOn' && <SortIcon />}
          endIcon={sortConfig.key === 'movesOn' && (
            sortConfig.direction === 'asc' ? '↑' : '↓'
          )}
        >
          Sort by Moves On
        </Button>
      )}
    </Box>
  );
};

export default SortingControls;
