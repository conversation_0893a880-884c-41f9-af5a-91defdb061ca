// import { useState,useRef, useEffect } from "react";
import { useEffect, useState, useMemo, useContext } from "react";
import { observer } from 'mobx-react';
import Contests from "./AdminView/Contests";
import Judges from "./AdminView/Judges";
import Groups from "./AdminView/Groups";
import Results from "./AdminView/Results"
import DownloadCodes from "./AdminView/DownloadCodes";
import { useTable } from "react-table";
import { ContestsColumns } from './AdminView/TableColumns';
import Toggle from 'react-toggle'
import "react-toggle/style.css"
import { AppProviderStore } from "../AppStore";
import { toJS } from "mobx";
import EntriesView from "./AdminView/EntriesView";
import UsersView from "./AdminView/UsersView";
import { TextField } from "@mui/material";

function AdminView({ option, data }) {
  const { AppStore } = useContext(AppProviderStore)
  const { categories, competitionState, setCompetitionState, setCompetitionMessage } = AppStore;
  console.log(toJS(competitionState))
  console.log(competitionState.registrationState)
  const tableData = useMemo(() => data, [data]);
  const tableColumns = useMemo(() => ContestsColumns, []);
  console.log(tableData, tableColumns)
  // const { getTableProps, getTableBodyProps, headerGroups, rows, prepareRow } = useTable({
  //   tableColumns, tableData
  // })
  async function toggleState(toToggle) {
    setCompetitionState(toToggle)
    console.log(toToggle)
  }

  async function updateMessage(key, message) {
    setCompetitionMessage(key, message)
    console.log(key, message)
  }

  return (
    <>
      <div className="bg-white">
        <div className="bg-header px-4 py-3">
          <h2 className="text-white font-semibold">{option.title}</h2>
        </div>
        <div className="py-4">
          <div className="flex">
            {option.title === "Contests" &&
              <div className="w-full">
                <div className='p-4 w-full grid grid-cols-[1fr_3fr_1fr] items-center gap-4'>
                  <div className="font-semibold text-black text-md">
                    Name
                  </div>
                  <div className="font-semibold text-black text-md">
                    Categories
                  </div>
                  <div className="font-semibold text-black text-md">
                    Price
                  </div> {/* New price header */}
                </div>
                {data?.map((item, index) => {
                  return (
                    <Contests contest={item} index={index} key={index} />
                  );
                })}

                <br />
                {/* Toggle Registration Section */}
                <div className="bg-header px-4 py-3">
                  <h2 className="text-white font-semibold">Toggle Registration:</h2>
                  <div className="bg-white p-4">
                    <div className="flex flex-col gap-6"> {/* Increased gap */}
                      <Toggle
                        defaultChecked={competitionState.registrationState}
                        onChange={() => toggleState('registrationState')}
                      />
                      <TextField
                        id="outlined-basic"
                        label={
                          competitionState.registrationState
                            ? "Registration Open Message"
                            : "Registration Closed Message"
                        }
                        variant="outlined"
                        defaultValue={competitionState.registrationMessage}
                        onChange={(event) => updateMessage('registrationMessage', event.target.value)}
                        fullWidth
                      />
                    </div>
                  </div>
                </div>
                <br />

                {/* Toggle Results Section */}
                <div className="bg-header px-4 py-3">
                  <h2 className="text-white font-semibold">Toggle Results:</h2>
                  <div className="bg-white p-4">
                    <div className="flex flex-col gap-6"> {/* Increased gap */}
                      <Toggle
                        defaultChecked={competitionState.resultState}
                        onChange={() => toggleState('resultState')}
                      />
                      <TextField
                        id="outlined-basic"
                        label="Result Message"
                        variant="outlined"
                        defaultValue={competitionState.resultMessage}
                        onChange={(event) => updateMessage('resultMessage', event.target.value)}
                        fullWidth
                      />
                    </div>
                  </div>
                </div>
              </div>
            }

            {option.title === "Judges" && <Judges />}
            {option.title === "Entries" &&
              <EntriesView />
            }
            {option.title === "Users" &&
              <UsersView />
            }
            {option.title === "Results" &&
              <div className="w-full">
                <div className='p-4  w-full grid grid-cols-[1fr_1fr_1fr_1fr_1fr] items-center gap-4 w-full'>
                  <div className="font-semibold text-black text-md">
                    Category
                  </div>
                  <div className="font-semibold text-black text-md">
                    Roster CSV
                  </div>
                  <div className="font-semibold text-black text-md">
                    Soloists CSV
                  </div>
                  <div className="font-semibold text-black text-md">
                    HTML
                  </div>
                </div>
                {categories?.map((category, index) => {
                  console.log(toJS(category))
                  return (
                    <Results category={category} index={index} key={category._id} />
                  );
                })}
              </div>
            }
            {
              option.title === "Download Codes" && <DownloadCodes />
            }
          </div>
        </div>
      </div >

    </>
  );
}

export default observer(AdminView);
