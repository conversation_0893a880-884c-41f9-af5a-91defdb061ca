import { BsCheckCircle } from "react-icons/bs";
import classNames from "classnames";
import { useState, useContext, Fragment, useEffect } from "react";
import { observer } from "mobx-react";
import { AppProviderStore } from "./../../AppStore";
import { toJS } from "mobx";

const GroupSidebar = () => {
    const { AppStore } = useContext(AppProviderStore);
    const {
        selectedGroup, setGroup,
        groups
    } = AppStore

    // useEffect(()=>{
    //     console.log('groupsidebar', toJS(groups))
    // },[groups])

    console.log('groupSidebar', toJS(groups))
    return (
        <div className="w-[270px] pt-10 min-h-full bg-sidebar">
            <ul>
                {groups.map(data => {
                    return (
                        <li
                            key={data.group._id}
                            onClick={() => setGroup(data)}
                            className={classNames(
                                "group flex items-center w-11/12 gap-2 font-semibold p-5 pr-0 mb-5 duration-300",
                                " hover:!bg-[#3b8c6e] hover:!text-white hover:w-[100%]",
                                selectedGroup?._id === data.group._id
                                    ? "bg-sidebarListItem text-white w-[100%]"
                                    : "bg-white text-grayText"
                            )}
                        >{data.group.ensembleName}</li>
                    )
                })}
            </ul>
        </div>
    )
}
export default observer(GroupSidebar);