import Classification from "../models/classification.mjs"

export default async function(fastify,opts){
    fastify.get("/api/classifications", async function (request, reply) {
        if (request.query.name) {
          const classification = await Classification.findOne(
            {
              name: request.query.name,
            },
            "_id"
          );
          reply.send(classification._id);
        } else {
          const categories = await Classification.find()
            .populate("classifications", { name: 1, _id: 0 })
            .lean();
    
          reply.send(categories);
        }
      });
}