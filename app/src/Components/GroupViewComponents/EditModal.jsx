import { useState, useEffect, useContext } from 'react';
import { observer } from 'mobx-react';
import { AppProviderStore } from "../../AppStore";
import { Modal, Box, Typography, TextField, Button } from '@mui/material';
import EditEntryForm from "./EditForms/EditEntryForm"
import EditContactForm from "./EditForms/EditContactForm"
import EditSelectionForm from "./EditForms/EditSelectionForm"
import { IconButton } from '@mui/material';
import CancelIcon from '@mui/icons-material/Cancel';

function EditModal({ open, handleClose, editing, userId }) {
    if(!editing) return null
    const { title, defaultValues, toEdit, hasSoloists, id } = editing;
    const renderForm = () => {
        console.log(hasSoloists)
        switch (toEdit.editing) {
            case 'entry':
                return <EditEntryForm defaultValues={defaultValues} route={toEdit.route} id={id} handleClose={handleClose}  />;
            case 'contact':
                return <EditContactForm defaultValues={defaultValues} route={toEdit.route} id={id} handleClose={handleClose}  />;
            case 'currTrack':
                return <EditSelectionForm defaultValues={defaultValues} route={toEdit.route} id={id} hasSoloists={hasSoloists} userId={userId} handleClose={handleClose} />;
            default:
                return null;
        }
    };

    return (
<Modal
    open={open}
    onClose={handleClose}
    aria-labelledby="modal-modal-title"
    aria-describedby="modal-modal-description"
>
    <Box sx={{ position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', width: 750, bgcolor: 'background.paper', boxShadow: 24, p: 4 }}>
        <Typography id="modal-modal-title" variant="h6" component="h2">
            {title}
        </Typography>
        <IconButton
            aria-label="close"
            onClick={handleClose}
            sx={{ position: 'absolute', top: 10, right: 10, color: 'red' }}
        >
            <CancelIcon />
        </IconButton>
        <div style={{ maxHeight: '70vh', overflow: 'auto', scrollbarWidth: 'thin', scrollbarColor: 'rgba(155, 155, 155, 0.5) rgba(255, 255, 255, 0.1)', paddingRight: '10px' }}>
            {renderForm()}
        </div>
    </Box>
</Modal>
    );
}

export default observer(EditModal);
