import mongoose from "mongoose";
const { Schema, model } = mongoose;

const invoiceSchema = new Schema({
  billTo: String,
  invoiceDate: {
    type: Date,
    default: new Date()
  },
  invoiceDue:{
    type: Date,
  },
  invoiceNo: {
    type: Number,
    unique: true
  },
  lineItems: {
    type:[{
      qty: Number,
      description: String,
      price: Number,
      total: Number
    }]
  },
  isPaid: {
    type: Boolean,
    default: false
  },
  total: Number
});

// Pre-save hook to generate invoice number and set due date
invoiceSchema.pre('save', async function(next) {
  try {
    // Incrementing invoice number
    if (!this.invoiceNo) {
      const lastInvoice = await Invoice.findOne().sort({invoiceNo: -1}).limit(1);
      if (lastInvoice) {
        this.invoiceNo = lastInvoice.invoiceNo + 1;
      } else {
        this.invoiceNo = 1;
      }
    }

    // Setting invoice due date to 30 days from invoice date
    if (!this.invoiceDue) {
      const invoiceDate = this.invoiceDate || new Date();
      this.invoiceDue = new Date(invoiceDate.getTime() + (30 * 24 * 60 * 60 * 1000));
    }

    next();
  } catch (error) {
    next(error);
  }
});

const Invoice = model("Invoice", invoiceSchema);
export default Invoice;
