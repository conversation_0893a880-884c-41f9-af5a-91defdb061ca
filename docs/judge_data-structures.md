# Data Structures Documentation

## Judge Data Structures

Judges can be assigned to categories and classifications in different ways. Below are the main patterns:

### 1. Simple Judge
A judge responsible for one category and all its classifications.

```javascript
{
  "_id": ObjectId,
  "name": String,
  "email": String,
  "assigned": [{
    "category": ObjectId,
    "classifications": [{
      "id": ObjectId
    }, ...]
  }],
  "judgeIntro": String | null,
  "isActive": Boolean
}
```

### 2. Multi-Category Judge
A judge responsible for multiple categories, with all classifications in each category.

```javascript
{
  "_id": ObjectId,
  "name": String,
  "email": String,
  "assigned": [{
    "category": ObjectId,
    "classifications": [{
      "id": ObjectId
    }, ...]
  }, {
    "category": ObjectId,
    "classifications": [{
      "id": ObjectId
    }, ...]
  }],
  "judgeIntro": String | null,
  "isActive": Boolean
}
```

### 3. Complex Judge Assignment
A judge with multiple categories, selected classifications, specific groups, and phase assignments.

```javascript
{
  "_id": ObjectId,
  "name": String,
  "email": String,
  "assigned": [{
    "category": ObjectId,
    "classifications": [{
      "id": ObjectId,
      "phase": Number,  // e.g., 2 for "Top 50%"
      "groups": [ObjectId, ...] // Specific groups assigned to this judge
    }, {
      "id": ObjectId  // Classification without phase or groups = all groups
    }, ...]
  }, ...]
  "judgeIntro": String | null,
  "isActive": Boolean
}
```

### Key Concepts:

1. **Phase**
   - When not specified: Judge reviews all groups
   - Phase 2: Typically means "Top 50%" or selected groups only

2. **Groups Array**
   - Present: Judge is assigned to specific groups only
   - Absent: Judge reviews all groups in that classification

3. **Classifications**
   - Simple assignment: Just includes classification ID
   - Complex assignment: Can include phase and specific groups

4. **Categories**
   - A judge can be assigned to multiple categories
   - Each category can have different classification assignments