import { useF<PERSON>, Controller } from 'react-hook-form';
import { Text<PERSON>ield, Button, Typography, Box, Select, MenuItem, FormControlLabel, Checkbox } from '@mui/material';
import { AppProviderStore } from '../../../AppStore';
import { observer } from 'mobx-react';
import { useContext } from 'react';
import Swal from "sweetalert2";

// List of state abbreviations
const stateAbbreviations = [
    'AL', 'AK', 'AZ', 'AR', 'CA', 'CO', 'CT', 'DE', 'FL', 'GA', 'HI', 'ID', 'IL', 'IN', 'IA', 'KS',
    'KY', 'LA', 'ME', 'MD', 'MA', 'MI', 'MN', 'MS', 'MO', 'MT', 'NE', 'NV', 'NH', 'NJ', 'NM', 'NY',
    'NC', 'ND', 'OH', 'OK', 'OR', 'PA', 'RI', 'SC', 'SD', 'TN', 'TX', 'UT', 'VT', 'VA', 'WA', 'WV', 'WI', 'WY'
];

function EditContactForm({ defaultValues, route, id, handleClose }) {
    const { register, handleSubmit, control, watch, setValue,formState: { dirtyFields, touchedFields }  } = useForm({ defaultValues });
    const {AppStore} = useContext(AppProviderStore)
    const {selectedGroup, updateSelectedGroup} = AppStore
    const onSubmit = async (data) => {
        let contactUpdate = {
            filter: {_id:selectedGroup._id},
            toUpdate:{}
        }
        let toSet = {}
        for ( const key in data){

            if(touchedFields[key]){
                toSet[key] = data[key]
            }
        }
        if (Object.keys(toSet).length !== 0) {
            contactUpdate.toUpdate.$set = { ...toSet }
        }
        try{
            let updateSuccessful = await updateSelectedGroup(contactUpdate)
            if(updateSuccessful){
                handleClose()
                Swal.fire({
                    title: "Success!",
                    text:"Your Contact Info has been updated successfully",
                    icon:"success"
                })
            }else{
                handleClose()
                Swal.fire({
                    title: "Error",
                    text: "There was an error updating your contact information",
                    html: 'Please contact <a href="mailto:<EMAIL>"><EMAIL></a> if problem persists',
                    confirmButtonText: 'Ok' ,
                    icon: 'error'
               })
            }
        }catch(err){
            console.error(err)
        }
    };

    const hasShippingAddress = watch('shippingAddress');

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <Controller
                name="cellphone"
                control={control}
                render={({ field }) => (
                    <TextField
                        {...field}
                        label="Cellphone"
                        variant="outlined"
                        margin="normal"
                        fullWidth
                    />
                )}
            />
            <Typography variant="subtitle1" gutterBottom>
                Mailing Address
            </Typography>
            {/* Mailing Address Fields */}
            <Controller
                name="mailingAddress.street"
                control={control}
                render={({ field }) => (
                    <TextField
                        {...field}
                        label="Street"
                        variant="outlined"
                        margin="normal"
                        fullWidth
                    />
                )}
            />
            <Controller
                name="mailingAddress.city"
                control={control}
                render={({ field }) => (
                    <TextField
                        {...field}
                        label="City"
                        variant="outlined"
                        margin="normal"
                        fullWidth
                    />
                )}
            />
            <Controller
                name="mailingAddress.state"
                control={control}
                render={({ field }) => (
                    <Select {...field} variant="outlined" fullWidth>
                        {stateAbbreviations.map(abbreviation => (
                            <MenuItem key={abbreviation} value={abbreviation}>
                                {abbreviation}
                            </MenuItem>
                        ))}
                    </Select>
                )}
            />
            <Controller
                name="mailingAddress.zip"
                control={control}
                render={({ field }) => (
                    <TextField
                        {...field}
                        label="ZIP"
                        variant="outlined"
                        margin="normal"
                        fullWidth
                    />
                )}
            />

            {/* Checkbox for indicating whether shipping address is different */}
            <FormControlLabel
                control={
                    <Checkbox
                        checked={hasShippingAddress ? true : false}
                        onChange={(e) => setValue('shippingAddress', e.target.checked ? {} : null)}
                    />
                }
                label="Shipping address is different from mailing"
            />
            {/* Shipping Address Fields */}
            {hasShippingAddress && (
                <>
                            <Typography variant="subtitle1" gutterBottom>
                Shipping Address
            </Typography>
                    <Controller
                        name="shippingAddress.street"
                        control={control}
                        render={({ field }) => (
                            <TextField
                                {...field}
                                label="Street"
                                variant="outlined"
                                margin="normal"
                                fullWidth
                            />
                        )}
                    />
                    <Controller
                        name="shippingAddress.city"
                        control={control}
                        render={({ field }) => (
                            <TextField
                                {...field}
                                label="City"
                                variant="outlined"
                                margin="normal"
                                fullWidth
                            />
                        )}
                    />
                    <Controller
                        name="shippingAddress.state"
                        control={control}
                        defaultValue=''
                        render={({ field }) => (
                            <Select {...field} variant="outlined" fullWidth>
                                {stateAbbreviations.map(abbreviation => (
                                    <MenuItem key={abbreviation} value={abbreviation}>
                                        {abbreviation}
                                    </MenuItem>
                                ))}
                            </Select>
                        )}
                    />
                    <Controller
                        name="shippingAddress.zip"
                        control={control}
                        render={({ field }) => (
                            <TextField
                                {...field}
                                label="ZIP"
                                variant="outlined"
                                margin="normal"
                                fullWidth
                            />
                        )}
                    />
                </>
            )}
            <Button 
                type="submit" 
                variant="contained" 
                fullWidth
                className='bg-iconGreen'
            >
                Submit
            </Button>
        </form>
    );
}

export default observer(EditContactForm);
