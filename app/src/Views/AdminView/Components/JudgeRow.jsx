import { observer } from 'mobx-react';
import { useState } from 'react';
import { 
    Dialog, 
    DialogTitle, 
    DialogContent, 
    DialogActions,
    IconButton,
    Button 
} from '@mui/material';
import { Email, BorderColorOutlined, Delete, Close } from '@mui/icons-material';

const JudgeRow = observer(({
    item,
    handleEditJudge,
    removeJudge,
    introDialogOpen,
    setIntroDialogOpen,
}) => {
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
    const [assignmentDialogOpen, setAssignmentDialogOpen] = useState(false);

    const handleDelete = async () => {
        await removeJudge(item._id);
        setDeleteDialogOpen(false);
    };

    const getCategoryName = (categoryId) => {
        const category = item.assigned?.find(cat => cat.category?._id === categoryId || cat.category === categoryId);
        return category?.category?.name || 'Unknown Category';
    };

    const getClassificationName = (categoryId, classificationId) => {
        const category = item.assigned?.find(cat => cat.category?._id === categoryId || cat.category === categoryId);
        const classification = category?.classifications?.find(c => c.id?._id === classificationId || c.id === classificationId);
        return classification?.id?.name || 'Unknown Classification';
    };

    return (
        <>
            <div className="p-4 w-full border border-dullGray grid grid-cols-[1.2fr_1.5fr_1fr_1fr_1fr_0.7fr] items-center gap-4 w-full hover:bg-dullGray">
                <div className="flex items-center font-normal text-black text-sm break-all">
                    {item.name}
                </div>
                <div className="flex items-center font-normal text-black text-sm break-all">
                    <a href={`mailto:${item.email}`} target="_blank" className="text-iconGreen hover:underline">{item.email}</a>
                </div>
                <div className="flex items-center font-normal text-black text-sm break-all">
                    <a
                        href={`https://app.foundationformusiceducation.org/judge?judgeId=${item._id}`}
                        className="text-iconGreen hover:underline"
                        target='_blank'
                    >
                        View Judge Page
                    </a>
                </div>
                <div className="flex items-center font-normal text-black text-sm break-all">
                    {item.judgeIntro ? (
                        <>
                            <button
                                onClick={() => setIntroDialogOpen(true)}
                                className="text-iconGreen hover:underline"
                            >
                                View Intro
                            </button>
                            <Dialog
                                open={introDialogOpen}
                                onClose={() => setIntroDialogOpen(false)}
                                maxWidth="md"
                                fullWidth
                            >
                                <DialogTitle>
                                    Video Introduction
                                    <IconButton
                                        aria-label="close"
                                        onClick={() => setIntroDialogOpen(false)}
                                        sx={{ position: "absolute", right: 8, top: 8 }}
                                    >
                                        <Close />
                                    </IconButton>
                                </DialogTitle>
                                <DialogContent>
                                    <video width="100%" controls>
                                        <source src={item.judgeIntro} type="video/mp4" />
                                        Your browser does not support the video tag.
                                    </video>
                                </DialogContent>
                            </Dialog>
                        </>
                    ) : (
                        "No Intro Set"
                    )}
                </div>
                <div className="flex items-center font-normal text-black text-sm break-all">
                    {item.assigned && item.assigned.length > 0 ? (
                        <button
                            onClick={() => setAssignmentDialogOpen(true)}
                            className="text-iconGreen hover:underline"
                        >
                            View Assignments
                        </button>
                    ) : (
                        <span className="text-grayText">Not Assigned</span>
                    )}
                </div>
                <div className="flex items-center gap-2 justify-center">
                    {item.name !== "Rick Yancey" && (
                        <>
                            <BorderColorOutlined
                                onClick={() => handleEditJudge(item)}
                                className="w-[20px] h-[20px] cursor-pointer"
                                sx={{ color: '#008544' }}
                            />
                            <Delete
                                className="w-[20px] h-[20px] cursor-pointer text-red-500"
                                color="error"
                                onClick={() => setDeleteDialogOpen(true)}
                            />
                        </>
                    )}
                </div>
            </div>

            {/* Assignment Details Dialog */}
            <Dialog
                open={assignmentDialogOpen}
                onClose={() => setAssignmentDialogOpen(false)}
                maxWidth="md"
                fullWidth
                PaperProps={{
                    style: {
                        borderRadius: '0.75rem',
                        backgroundColor: '#ffffff',
                    },
                }}
            >
                <DialogTitle className="bg-sidebar text-white px-6 py-4 flex justify-between items-center">
                    <span>Assignments for {item.name}</span>
                    <IconButton
                        aria-label="close"
                        onClick={() => setAssignmentDialogOpen(false)}
                        sx={{ color: '#ffffff' }}
                    >
                        <Close />
                    </IconButton>
                </DialogTitle>
                <DialogContent className="p-6">
                    {item.assigned?.map((assignment, index) => (
                        <div key={index} className="mb-8 last:mb-0">
                            <div className="bg-sidebarListItem text-white px-4 py-2 rounded-t-lg">
                                <h3 className="font-medium">
                                    {getCategoryName(assignment.category?._id || assignment.category)}
                                </h3>
                            </div>
                            <div className="border border-t-0 border-listBorderColor rounded-b-lg">
                                {assignment.classifications?.map((classification, classIndex) => (
                                    <div key={classIndex} className="p-4 border-b border-listBorderColor last:border-b-0">
                                        <div className="font-medium text-black mb-2">
                                            {getClassificationName(
                                                assignment.category?._id || assignment.category,
                                                classification.id?._id || classification.id
                                            )}
                                        </div>
                                        <div className="space-y-1">
                                            {classification.phase && (
                                                <div className="text-grayText">
                                                    Phase: {classification.phase}
                                                </div>
                                            )}
                                            <div className="text-grayText">
                                                Groups: {classification.groups?.length ? 
                                                    `${classification.groups.length} specific groups assigned` : 
                                                    'All groups'}
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    ))}
                </DialogContent>
                <DialogActions className="p-4 border-t border-listBorderColor">
                    <button
                        onClick={() => setAssignmentDialogOpen(false)}
                        className="bg-sidebarListItem text-white px-6 py-2 rounded-lg hover:bg-opacity-90 transition-colors"
                    >
                        Close
                    </button>
                </DialogActions>
            </Dialog>

            {/* Delete Confirmation Dialog */}
            <Dialog
                open={deleteDialogOpen}
                onClose={() => setDeleteDialogOpen(false)}
                PaperProps={{
                    style: {
                        borderRadius: '0.75rem',
                    },
                }}
            >
                <DialogTitle className="bg-sidebar text-white px-6 py-4">
                    Confirm Delete
                </DialogTitle>
                <DialogContent className="p-6">
                    <p className="text-black">Are you sure you want to remove this judge?</p>
                </DialogContent>
                <DialogActions className="p-4 border-t border-listBorderColor">
                    <button
                        onClick={() => setDeleteDialogOpen(false)}
                        className="px-6 py-2 rounded-lg border border-sidebarListItem text-sidebarListItem hover:bg-opacity-90 transition-colors mr-2"
                    >
                        Cancel
                    </button>
                    <button
                        onClick={handleDelete}
                        className="bg-red text-white px-6 py-2 rounded-lg hover:bg-opacity-90 transition-colors"
                    >
                        Delete
                    </button>
                </DialogActions>
            </Dialog>
        </>
    );
});

export default JudgeRow;
