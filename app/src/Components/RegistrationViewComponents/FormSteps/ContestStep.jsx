import { useContext, useEffect } from "react";
import { useF<PERSON><PERSON>ontex<PERSON>, Controller } from "react-hook-form";
import { AppProviderStore } from "../../../AppStore";
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Box,
  Typography,
  FormHelperText,
  InputAdornment,
  TextField
} from "@mui/material";

function ContestStep({ groupNumber }) {
  const { AppStore } = useContext(AppProviderStore);
  const { formOptions } = AppStore;

  const {
    register,
    watch,
    control,
    setValue,
    getValues,
    formState: { errors }
  } = useFormContext();

  const selectedContest = watch(`group_${groupNumber}.contest`, "");
  const selectedCategory = watch(`group_${groupNumber}.category`, "");
  const selectedClassification = watch(`group_${groupNumber}.classification`, "");

  // Calculate registration fee when contest or classification changes
  useEffect(() => {
    let price = 0;

    // Find the price for the selected contest
    if (selectedContest) {
      for (const key in formOptions) {
        if (formOptions[key]._id === selectedContest) {
          // Ensure price is a number
          price = parseFloat(formOptions[key].price) || 0;
          break;
        }
      }
    }

    // Double the price if two classifications are selected
    if (Array.isArray(selectedClassification) && selectedClassification.length === 2) {
      price = price * 2;
    }

    // Update the registration fee field - ensure it's a number
    setValue(`group_${groupNumber}.registrationFee`, Number(price), { shouldDirty: true });
  }, [selectedContest, selectedClassification, formOptions, setValue, groupNumber]);

  const setDisabled = (classificationId) => {
    if (!classificationId) return false;

    let isDisabled = false;
    const aClasses = ["643453f8dd98890aad8cef20", "643453f8dd98890aad8cef21", "643453f8dd98890aad8cef22", "643453f8dd98890aad8cef23", "643453f8dd98890aad8cef24"];
    const newClasses = ["643453f8dd98890aad8cef25", "643453f8dd98890aad8cef26"];

    // Handle case when selectedClassification is not defined or not an array
    if (!selectedClassification || !Array.isArray(selectedClassification)) {
      return false;
    }

    // If we already have 2 classifications selected
    if (selectedClassification.length > 1) {
      // If this classification is already selected, it should not be disabled
      if (selectedClassification.includes(classificationId)) {
        return false;
      } else {
        // Otherwise, disable it since we can only select 2
        return true;
      }
    }

    // If we have 0 or 1 classification selected
    const selected = selectedClassification[0];

    // If no classification is selected or this is the selected one, don't disable
    if (!selected || selected === classificationId) {
      return isDisabled;
    } else {
      // Check if the selected and current classifications are in the same group
      if (aClasses.includes(classificationId)) {
        isDisabled = aClasses.includes(selected);
      } else if (newClasses.includes(classificationId)) {
        isDisabled = newClasses.includes(selected);
      }
    }

    return isDisabled;
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
      <Typography variant="h6" gutterBottom>
        Contest Details
      </Typography>

      <Typography variant="body1" gutterBottom>
        Please select the contest, category, and classification for your ensemble.
      </Typography>

      <Controller
        name={`group_${groupNumber}.contest`}
        control={control}
        rules={{ required: "Contest is required" }}
        render={({ field, fieldState: { error } }) => {
          // Safely extract value and onChange from field
          const value = field?.value || "";
          const onChange = field?.onChange || (() => {});

          return (
            <FormControl error={!!error} fullWidth>
              <InputLabel required>Select Contest</InputLabel>
              <Select
                value={value}
                onChange={onChange}
                onBlur={field?.onBlur}
                name={field?.name}
                ref={field?.ref}
                required
                label="Select Contest"
              >
                <MenuItem value="" disabled>
                  Select Contest
                </MenuItem>
                {Object.keys(formOptions).map((key) => (
                  <MenuItem key={formOptions[key]._id} value={formOptions[key]._id}>
                    {formOptions[key].name}
                  </MenuItem>
                ))}
              </Select>
              {error && <FormHelperText>{error.message}</FormHelperText>}
            </FormControl>
          );
        }}
      />

      <Controller
        name={`group_${groupNumber}.category`}
        control={control}
        rules={{ required: "Category is required" }}
        render={({ field, fieldState: { error } }) => {
          // Safely extract value and onChange from field
          const value = field?.value || "";
          const onChange = field?.onChange || (() => {});

          return (
            <FormControl error={!!error} fullWidth>
              <InputLabel required>Select Category</InputLabel>
              <Select
                value={value}
                onChange={onChange}
                onBlur={field?.onBlur}
                name={field?.name}
                ref={field?.ref}
                required
                label="Select Category"
                disabled={!selectedContest}
              >
                <MenuItem value="" disabled>
                  Select Category
                </MenuItem>
                {selectedContest &&
                  formOptions
                    .find((option) => option._id === selectedContest)
                    ?.categories.map((category) => (
                      <MenuItem key={category._id} value={category._id}>
                        {category.name}
                      </MenuItem>
                    ))}
              </Select>
              {error && <FormHelperText>{error.message}</FormHelperText>}
            </FormControl>
          );
        }}
      />

      {selectedCategory === "643456dbdd98890aad8cef31" ? ( // National wind band honors
        <Controller
          name={`group_${groupNumber}.classification`}
          control={control}
          rules={{ required: "Classification is required" }}
          defaultValue={[]}
          render={({ field, fieldState: { error } }) => {
            // Safely extract value and onChange from field
            const value = field?.value;
            const onChange = field?.onChange || (() => {});

            // Ensure value is always an array
            const safeValue = Array.isArray(value) ? value : [];

            return (
              <FormControl error={!!error} fullWidth>
                <InputLabel>Select Classification</InputLabel>
                <Select
                  labelId="select-classification-label"
                  id={`select-classification-${groupNumber}`}
                  label="Select Classification"
                  multiple
                  value={safeValue}
                  onChange={(event) => {
                    const selectedValues = event.target.value;
                    if (selectedValues && selectedValues.length <= 2) {
                      onChange(selectedValues);
                    }
                  }}
                  renderValue={(selected) => {
                    // Ensure selected is an array
                    const safeSelected = Array.isArray(selected) ? selected : [];

                    return (
                      <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
                        {safeSelected.filter(Boolean).map((val) => {
                          // Find the classification name safely
                          let classificationName = "";
                          try {
                            if (val && selectedContest && selectedCategory) {
                              const contest = formOptions.find(option => option._id === selectedContest);
                              if (contest && contest.categories) {
                                const category = contest.categories.find(cat => cat._id === selectedCategory);
                                if (category && category.classifications) {
                                  const classification = category.classifications.find(cls => cls._id === val);
                                  if (classification) {
                                    classificationName = classification.name;
                                  }
                                }
                              }
                            }
                          } catch (error) {
                            console.error("Error finding classification name:", error);
                          }

                          return (
                            <Chip
                              key={val || "empty"}
                              label={classificationName || val || ""}
                            />
                          );
                        })}
                      </Box>
                    );
                  }}
                >
                  <MenuItem value="" disabled>
                    Select up to two Classification
                  </MenuItem>
                  {selectedCategory &&
                    formOptions
                      .find((option) => option._id === selectedContest)
                      ?.categories.find(
                        (category) => category._id === selectedCategory
                      )
                      ?.classifications.map((classification) => (
                        <MenuItem
                          key={classification._id}
                          value={classification._id}
                          disabled={setDisabled(classification._id)}
                        >
                          {classification.name}
                        </MenuItem>
                      ))}
                </Select>
                {error ? (
                  <FormHelperText error>{error.message}</FormHelperText>
                ) : (
                  <FormHelperText>Can Select up to 2 (ex: A Class + New Music Class)</FormHelperText>
                )}
              </FormControl>
            );
          }}
        />
      ) : (
        <Controller
          name={`group_${groupNumber}.classification`}
          control={control}
          rules={{ required: "Classification is required" }}
          render={({ field, fieldState: { error } }) => {
            // Safely extract value and onChange from field
            const value = field?.value || "";
            const onChange = field?.onChange || (() => {});

            return (
              <FormControl error={!!error} fullWidth>
                <InputLabel required>Select Classification</InputLabel>
                <Select
                  value={value}
                  onChange={onChange}
                  onBlur={field?.onBlur}
                  name={field?.name}
                  ref={field?.ref}
                  required
                  label="Select Classification"
                  disabled={!selectedCategory}
                >
                  <MenuItem value="" disabled>
                    Select Classification
                  </MenuItem>
                  {selectedCategory &&
                    formOptions
                      .find((option) => option._id === selectedContest)
                      ?.categories.find(
                        (category) => category._id === selectedCategory
                      )
                      ?.classifications.map((classification) => (
                        <MenuItem
                          key={classification._id}
                          value={classification._id}
                        >
                          {classification.name}
                        </MenuItem>
                      ))}
                </Select>
                {error && <FormHelperText error>{error.message}</FormHelperText>}
              </FormControl>
            );
          }}
        />
      )}

      <Controller
        name={`group_${groupNumber}.registrationFee`}
        control={control}
        render={({ field }) => (
          <TextField
            label="Registration Fee"
            aria-readonly
            disabled
            value={field.value || 0}
            onChange={(e) => {
              // Parse the input value to a number
              const numValue = parseFloat(e.target.value.replace(/[^0-9.]/g, ''));
              field.onChange(isNaN(numValue) ? 0 : numValue);
            }}
            InputProps={{
              startAdornment: <InputAdornment position="start">$</InputAdornment>,
            }}
          />
        )}
      />
    </Box>
  );
}

export default ContestStep;
