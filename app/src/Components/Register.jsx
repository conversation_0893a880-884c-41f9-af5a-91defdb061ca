import { useState, useEffect, useContext, useRef } from "react";
import { useForm } from "react-hook-form";
import { AppProviderStore } from "../AppStore";
import { observer } from "mobx-react";
import axios from "axios";
import { useNavigate } from "react-router-dom";
import Swal from "sweetalert2";
import { FaEye, FaEyeSlash } from "react-icons/fa";

function Register({ toggleForm }) {
  const {
    register,
    handleSubmit,
    getValues,
    formState: { errors, isSubmitting, isValid },
  } = useForm();
  const { AppStore } = useContext(AppProviderStore);
  const navigate = useNavigate();
  const { user, setUser } = AppStore;

  const [passwordShown, setPasswordShown] = useState(false);

  const togglePasswordVisiblity = () => {
    setPasswordShown(!passwordShown);
  };

  const [passwordStatus, setPasswordStatus] = useState({
    minLength: false,
    hasCapitalLetter: false,
    hasNumber: false,
    hasSpecialChar: false,
  });

  const updatePasswordStatus = (value) => {
    setPasswordStatus({
      minLength: value.length >= 8,
      hasCapitalLetter: /[A-Z]/.test(value),
      hasNumber: /\d/.test(value),
      hasSpecialChar: /[@$!%*#?&]/.test(value),
    });
  };

  const onSubmit = async (data) => {
    try {
      const response = await axios.post("/api/createUser", data);

      if (response.status === 200) {
        setUser(response.data);
        navigate("/account");
      }
    } catch (error) {
      if (error.response && error.response.status === 400) {
        Swal.fire({
          icon: "error",
          title: "Error creating an account:",
          text: "A user already exists with this email",
        });
      }
    }
  };

  return (
    <div className="container mx-auto p-8 flex flex-col items-center">
      <form onSubmit={handleSubmit(onSubmit)} className="flex gap-5 flex-col w-full">
        <input
          {...register("email", { required: true })}
          type="email"
          placeholder="Enter your email"
          className="p-3 border-0 w-full rounded border"
        />
        {errors.email && <span>This field is required</span>}

        <input
          {...register("password", {
            required: true,
            minLength: 8,
            pattern: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]/,
          })}
          type={passwordShown ? "text" : "password"}
          placeholder="Enter your password"
          className="p-3 border-0 w-full rounded border"
          onChange={(e) => updatePasswordStatus(e.target.value)}
        />
        <div className="flex flex-col text-white text-sm">
          <div
            className={`mb-1 flex items-center ${
              passwordStatus.minLength ? "text-green-500" : "text-red-500"
            }`}
          >
            <div className="w-4 h-4 mr-1">
              {passwordStatus.minLength ? "✓" : "✗"}
            </div>
            At least 8 characters
          </div>
          <div
            className={`mb-1 flex items-center ${
              passwordStatus.hasCapitalLetter ? "text-green-500" : "text-red-500"
            }`}
          >
            <div className="w-4 h-4 mr-1">
              {passwordStatus.hasCapitalLetter ? "✓" : "✗"}
            </div>
            One capital letter
          </div>
          <div
            className={`mb-1 flex items-center ${
              passwordStatus.hasNumber ? "text-green-500" : "text-red-500"
            }`}
          >
            <div className="w-4 h-4 mr-1">
              {passwordStatus.hasNumber ? "✓" : "✗"}
            </div>
            One number
          </div>
          <div
            className={`mb-1 flex items-center ${
              passwordStatus.hasSpecialChar ? "text-green-500" : "text-red-500"
            }`}
          >
            <div className="w-4 h-4 mr-1">
              {passwordStatus.hasSpecialChar ? "✓" : "✗"}
            </div>
            One special character
          </div>
        </div>

        {errors.password && (
          <span>
            {errors.password.type === "minLength"
              ? "Password must be at least 8 characters"
              : "Password must include a number and a symbol"}
          </span>
        )}

        <input
          {...register("confirmPassword", {
            required: true,
            validate: (value) => value === getValues("password"),
          })}
          type={passwordShown ? "text" : "password"}
          placeholder="Confirm password"
          className="p-3 border-0 w-full rounded border"
        />

        {errors.confirmPassword && <span>Passwords must match</span>}
        <span
          onClick={togglePasswordVisiblity}
          className="flex text-white items-center cursor-pointer"
        >
          {passwordShown ? <FaEyeSlash /> : <FaEye />}
          <span className="ml-2">
            {passwordShown ? "Hide Password" : "Show Password"}
          </span>
        </span>
        <button
          type="submit"
          className={`p-5 w-1/2 bg-iconGreen text-white font-semibold rounded-3xl${
            !isValid ? "" : "hover:bg-green-600"
          } ${isSubmitting ? "cursor-not-allowed opacity-50" : ""}`}
          disabled={!isValid || isSubmitting}
        >
          Register
        </button>
      </form>
      <p className="text-white mt-4 cursor-pointer" onClick={toggleForm}>
        Already registered? Login here.
      </p>
    </div>
  );
}

export default observer(Register);
