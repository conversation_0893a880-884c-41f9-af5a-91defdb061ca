import { useState, useEffect, useContext, useRef } from "react";
import axios from "axios";
import GroupView from "../Views/GroupView";
import RegistrationView from "../Views/RegistrationView";
import AppHeader from "../Components/Header/Header";
import { AppProviderStore } from "../AppStore";
import { observer } from 'mobx-react';
import { toJS } from "mobx";
import Swal from 'sweetalert2';
import LogRocket from 'logrocket';
import { useNavigate } from "react-router-dom";
import AccountSidebar from "../Components/Sidebar/AccountSidebar";
import { jwtDecode } from "jwt-decode";
import { Link } from "react-router-dom";


function Account() {
  const { AppStore } = useContext(AppProviderStore);
  const [showRegistration, setRegistration] = useState(false)
  const [welcomeMessage, setWelcomeMessage] = useState();
  const {
    user,
    selectedGroup, setGroup,
    groups, setGroups,
    menuSelected, setSelectedMenu,
    competitionState, getCompetitionState,
  } = AppStore;
  const navigate = useNavigate();

  useEffect(() => {
    console.log('user useEffect')
    if (!user) {
      navigate('/')
    } else {
      console.log('setting log rocket')
      LogRocket.identify(user.email)
      getCompetitionState();
      if (user.groups) {
        console.log('setting groups')
        console.log(toJS(user.groups))
        setGroups(user.groups)
      }

    }
  }, [user])
  
  useEffect(()=>{
    console.log(toJS(competitionState))
    if(!competitionState.registrationState && !competitionState.resultState){
      setWelcomeMessage(competitionState.registrationMessage)
    }
    if (competitionState.registrationState) {
      setWelcomeMessage(competitionState.registrationMessage);
    }
    if (competitionState.resultState) {
      setWelcomeMessage(competitionState.resultMessage);
    }
  },[competitionState])

  function toggleRegistrationView() {
    console.log('togggleeeeee')
    setRegistration(!showRegistration)
  }


  return (
    <>
      <div className="app_container">
        <div className="main_app h-full min-h-[100vh]">
          <AppHeader />
          <div className="grid grid-cols-[270px_1fr] pt-[90px] h-full grid-rows-1 min-h-[100vh]">
            <AccountSidebar toggleRegistrationView={toggleRegistrationView} showRegistration={showRegistration} />
            {selectedGroup._id && !showRegistration ? (
              <div className="p-5 bg-mainContent main_content overflow-auto">
                <GroupView group={AppStore.selectedGroup} menuSelected={menuSelected} />
              </div>
            ) : (
              <div className="p-5 bg-mainCosntent main_content overflow-auto flex items-center justify-center">
                {showRegistration ? (
                  <RegistrationView toggleRegistrationView={toggleRegistrationView} />
  
                ) : (
                  <div className="bg-white rounded-2xl w-[50%] h-[50%] flex flex-col items-center justify-center shadow-[0_1px_8px_0px_rgba(170,170,170,1)]">
                    <p className="text-iconGreen font-semibold px-4 py-1 w-fit rounded-3xl border border-1 border-iconGreen mb-4">
                      {welcomeMessage}
                    </p>
                    {/* Conditional rendering of the green "Register Group" button */}
                    {competitionState.registrationState || user?.isAdmin ? (
                      <div className="flex space-x-4">
                        <button
                          className="p-3 bg-iconGreen text-white font-semibold rounded-lg"
                          onClick={toggleRegistrationView}
                        >
                          Register Now
                        </button>
                        <button
                          className="p-3 bg-iconGreen text-white font-semibold rounded-lg"
                          onClick={() => {
                            window.open(`https://www.foundationformusiceducation.org/rules-and-guideline`, '_blank')
                          }}
                        >
                          Rules and Guidelines
                        </button>
                      </div>
                    ):                       <div className="flex space-x-4">
                      <Link to="/results">
                        <button
                          className="p-3 bg-iconGreen text-white font-semibold rounded-lg"
                        >
                          See Winners
                        </button>
                        </Link>
                      </div>
                    }
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}

export default observer(Account);

