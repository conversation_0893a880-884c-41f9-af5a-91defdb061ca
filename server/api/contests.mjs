import Contest from "../models/contest.mjs";

export default async function (fastify, opts) {
  // Get All Contests
  fastify.get("/api/contests", async function (request, reply) {
    if (request.query.name) {
      const contest = await Contest.findOne(
        {
          name: request.query.name,
        },
        "_id"
      );
      reply.send(contest._id);
    } else {
      const contests = await Contest.find()
        .populate("categories", { name: 1 })
        .lean();
      console.log('requesting contests');
      console.log(contests);
      reply.send(contests);
    }
  });

  // Populate All Contests
  fastify.get('/api/contests/populateAll', async function(request, response) {
    const fullList = await Contest.find()
      .populate({
        path: "categories",
        select: { name: 1 },
        populate: {
          path: "classifications",
          select: { name: 1 }
        }
      })
      .lean();
    response.send(fullList);
  });

  // Update Contest Price
  fastify.patch('/api/contests/:id/price', async function(request, reply) {
    const { id } = request.params; // Get the contest ID from the URL params
    const { price } = request.body; // Get the new price from the request body
    console.log(price, typeof price)

    try {
      // Find the contest and update the price
      const updatedContest = await Contest.findByIdAndUpdate(
        id,
        { price },
        { new: true } // Return the updated document
      );

      if (!updatedContest) {
        return reply.status(404).send({ error: "Contest not found" });
      }

      // Send the updated contest as the response
      reply.send(updatedContest);
    } catch (error) {
      console.error(error);
      reply.status(500).send({ error: "Failed to update price" });
    }
  });
}
