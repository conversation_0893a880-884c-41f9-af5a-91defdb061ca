import React, { useEffect, useRef } from 'react';

const AudioPlayerIframe = ({ trackSrc }) => {
  const iframeRef = useRef();

  useEffect(() => {
    if (iframeRef.current) {
      const iframeDoc = iframeRef.current.contentWindow.document;
      iframeDoc.open();
      iframeDoc.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <style>
            .w-3-4 { width: 75%; }
            body { display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0; }
          </style>
        </head>
        <body>
          <audio class="w-3-4" src="${trackSrc}" controls></audio>
        </body>
        </html>
      `);
      iframeDoc.close();
    }
  }, [trackSrc]);

  return <iframe ref={iframeRef} title="Audio Player" />;
};

export default AudioPlayerIframe;
