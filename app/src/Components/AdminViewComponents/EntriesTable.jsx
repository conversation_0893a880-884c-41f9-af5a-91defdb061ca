import { useState, useEffect, use<PERSON><PERSON>back, useContext } from "react";
import { observer } from "mobx-react";
import {
  DataGrid,
  GridToolbarContainer,
  GridFooterContainer,
  GridFooter,
  GridToolbarColumnsButton,
  GridToolbarFilterButton,
  GridToolbarQuickFilter,
  GridToolbarExport,
  GridToolbarExportContainer,
  GridCsvExportMenuItem,
  GridToolbarDensitySelector,
  GridLoadingOverlay,
  GridNoRowsOverlay,
  GridNoResultsOverlay,
  useGridApiRef
} from '@mui/x-data-grid';
import { Box, Button, Typography,MenuItem } from "@mui/material";
import { Snackbar, Alert } from "@mui/material";
import { AppProviderStore } from "../../AppStore";
import { toJS } from "mobx";
import axios from "axios";
import { genDataGridProps } from "../../lib/tableHelper.mjs";
import { fetchEntries } from '../../lib/api';

function EntriesTable({ category, classification }) {
  const { AppStore } = useContext(AppProviderStore);
  const { contests, categories } = AppStore
  const selectOptions = {
    contests: contests,
    categories: categories
  }
  const apiRef = useGridApiRef()
  const [entries, setEntries] = useState([]);
  const [loading, setLoading] = useState(true);
  const currentYear = new Date().getFullYear();
  const [filterYear, setFilterYear] = useState(currentYear);
  const [isArchived, setIsArchived] = useState(false);

  const [snackbar, setSnackbar] = useState(null);
  const handleCloseSnackbar = () => setSnackbar(null)
  const [tableData, setTableData] = useState({ columns: [], rows: [] })
  const [totalDuration, setTotalDuration] = useState({});

  // const [sortModel, setSortModel] = useState([{
  //   field: 'classification',
  //   sort: 'desc'
  // }])


  useEffect(() => {
    // console.log('fetch entries')
    fetchEntries(category, classification, filterYear, isArchived)
      .then((data) => {
        setEntries(data.groups);
        setTotalDuration(data.totalDuration);
      })
      .catch(() => setLoading(false));
  }, [category, classification, filterYear, isArchived]);

  useEffect(() => {
    setLoading(false)
    // console.log('set table')
    let contest = entries[0]?.contest.name
    let tData = genDataGridProps('entries', entries, selectOptions, isArchived, handleArchiveRestoreClick,contest)
    setTableData(tData);
  }, [entries]);

  useEffect(() => {
    setEntries([]);
    let tData = genDataGridProps('entries', [], selectOptions, isArchived, handleArchiveRestoreClick)
    setTableData(tData);
  }, [category, filterYear, isArchived]);

  function MailingListCSV({ apiRef, hideMenu }) {
    const categoryName = categories.find(cat => cat._id === category).name || 'FME'
    const handleMailingListExport = () => {
      apiRef.current.exportDataAsCsv({
        fields: [
          'schoolName', 'ensembleName', 'director',
          'mailingAddress.street', 'mailingAddress.city', 'mailingAddress.state', 'mailingAddress.zip',
          'shippingAddress.street', 'shippingAddress.city', 'shippingAddress.state', 'shippingAddress.zip',
        ],
        fileName: `${categoryName}_MailingList`,
        includeColumnGroupsHeaders: false
      });
      if (hideMenu) hideMenu(); // Close the menu after export
    };

    return (
      <MenuItem onClick={handleMailingListExport}>
        Mailing List
      </MenuItem>
    );
  }
  function CustomExportMenu(props) {
    return (
      <GridToolbarExportContainer {...props}>
        <GridCsvExportMenuItem />
        <MailingListCSV apiRef={apiRef} />
      </GridToolbarExportContainer>
    )
  }
  function CustomToolbar() {
    const toggleArchived = () => {
      setIsArchived(prevState => !prevState)
    }
    return (
      <GridToolbarContainer>
        {/* <GridToolbarColumnsButton /> */}
        {/*filter button needs some looking into */}
        {/* <GridToolbarFilterButton /> */}
        <GridToolbarQuickFilter />
        {/* <GridToolbarDensitySelector /> */}

        <Box sx={{ flexGrow: 1 }} />
        <Button
          variant="outlined"
          onClick={toggleArchived}
        >
          {isArchived ? 'View Active' : 'View Archived'}
        </Button>
        <CustomExportMenu />
        {/* <GridToolbarExport
          slotProps={{
            tooltip: { title: 'Export data' },
            button: { variant: 'outlined' },
          }}
        /> */}
      </GridToolbarContainer>
    )
  }
  const handleArchiveRestoreClick = async (row, archivestate) => {
    let action = archivestate ? 'Archiving' : 'Restoring'
    try {
      //console.log('archiverestoreclicked', row);
      let archiveRequest = { filter: { _id: row._id }, toUpdate: { $set: { isArchived: archivestate } } };
      await axios.put(`/api/groups/adminUpdate`, archiveRequest);
      // Remove the archived entry from the state
      setEntries(entries.filter(entry => entry._id !== row._id))
      setSnackbar({ children: `${action} ${row.schoolName} - ${row.ensembleName} was successful`, severity: 'success' })
      // setEntries(entries.filter(entry => entry._id !== row._id));
    } catch (error) {
      let message = `${action} error: ${error}`
      setSnackbar({ children: message, severity: 'error' })
      console.error(`Error ${action}: `, error);
    }
  };


  const handleEdit = useCallback(async (newRow, oldRow) => {
    let groupId = newRow._id
    let groupUpdate = { filter: { _id: groupId }, toUpdate: {}, update: false }
    let trackUpdate = { filter: {}, toUpdate: {}, update: false }
    let updatedRow
    for (const key in newRow) {
      let updatedValue = newRow[key]
      if (Object.prototype.hasOwnProperty.call(newRow, key) && !Object.prototype.hasOwnProperty.call(oldRow, key)) {
        //console.log(key, 'doesnt exist in previous entry')
        if (key.includes('track')) {
          //console.log('doing something with tracks')
          let trackMatch = key.match(/track_(\d+)_(.+)/);
          let updateKey
          if (trackMatch) {
            let index = parseInt(trackMatch[1])
            trackUpdate.index = index
            let trackKey = trackMatch[2]
            let trackId = oldRow.tracks[index]._id
            trackUpdate.filter._id = trackId
            if (trackKey.includes('soloist')) {
              //console.log('doing something with soloists inside tracks')
              let soloistMatch = trackKey.match(/soloist_(\d+)_(.+)/);
              let soloistIndex = parseInt(soloistMatch[1])
              let soloistKey = soloistMatch[2]
              updateKey = `soloists.${soloistIndex}.${soloistKey}`
              // updatedRow.tracks[index].soloists[soloistIndex][soloistKey] = updatedValue
            } else {
              updateKey = trackKey
            }
            //console.log('finalize track update')
            trackUpdate.toUpdate = { $set: { [updateKey]: updatedValue } }
            trackUpdate.update = true
            //console.log(trackUpdate)
          }
        } else if (key.includes('maskedName')) {
          //console.log('this is a nested masked name update')
          let match = parseInt(key.match(/\d+/))
          let index = match ? match : 0
          groupUpdate.toUpdate = { $set: { [`classifications.${index}.maskedName`]: newRow[key].toUpperCase() } }
          //temp till I make update (debating on if I want to do a single update or a save and update all at once)
          updatedRow.classifications[index].maskedName = newRow[key].toUpperCase()
          groupUpdate.update = true
        } else {
          //console.log('key doesnt exist in old object but isnt track related and not nested')
          groupUpdate.update = true
          groupUpdate.toUpdate = { $set: { [key]: updatedValue } }
        }


      } else if (oldRow[key] !== newRow[key]) {
        if (key === "maskedName") {
          //console.log('masked name was updated')
          updatedValue = updatedValue.toUpperCase()
        }
        groupUpdate.toUpdate = { $set: { [key]: updatedValue } }
        groupUpdate.update = true
      }
    }

    if (groupUpdate.update) {
      //console.log('call updateGroup route')
      //console.log(groupUpdate)
      let res = await axios.put(`/api/groups/adminUpdate`, groupUpdate)
      updatedRow = res.data
      //console.log(updatedRow)
    }

    if (trackUpdate.update) {
      //console.log('call updateTrack route')
      //console.log(trackUpdate)
      let res = await axios.put(`/api/tracks/adminUpdate`, trackUpdate)
      let updatedTrack = res.data
      updatedRow = { ...oldRow }
      updatedRow.tracks[trackUpdate.index] = updatedTrack
    }
    setSnackbar({ children: `${updatedRow.schoolName} - ${updatedRow.ensembleName} updated succesfully`, severity: 'success' })
    return updatedRow

  })

  const handleProcessRowUpdateError = useCallback((error) => {
    //console.log('Update Erro', error)
    setSnackbar({ children: error, severity: 'error' })
  }, [])

  const CustomFooterComponent = () => {
    //console.log(totalDuration)
    return (
<GridFooterContainer>
  <Box sx={{ p: 1, display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
    <Box sx={{ p: 1 }}>
      <Typography variant="body2">Total Duration: {totalDuration.duration}</Typography>
      {totalDuration.notice && totalDuration.notice.map((msg, index) => (
        <Typography key={index} variant="body2" color="error">{msg}</Typography>
      ))}
    </Box>
    <GridFooter sx={{ flexShrink: 0 }} />
  </Box>
</GridFooterContainer>

    );
  }

  return (
    <div>
      <DataGrid
        {...tableData}
        apiRef={apiRef}
        getRowId={(row) => row._id}
        // initialState={{
        //   sorting: {
        //     sortModel: [{ field: 'classification', sort: 'desc' }],
        //   }
        // }}
        // sortModel={sortModel}
        // onSortModelChange={(newModel)=>setSortModel(newModel)}
        slots={{
          loadingOverlay: GridLoadingOverlay,
          toolbar: CustomToolbar,
          noRowsOverlay: GridNoRowsOverlay,
          noResultsOverlay: GridNoResultsOverlay,
          footer: CustomFooterComponent
        }}
        // slotProps={{
        //   footer:{totalDuration}
        // }}
        processRowUpdate={handleEdit}
        onProcessRowUpdateError={handleProcessRowUpdateError}
      />
      {!!snackbar && (
        <Snackbar open anchorOrigin={{ vertical: "bottom", horizontal: "center" }} onClose={handleCloseSnackbar} autoHideDuration={6000}>
          <Alert {...snackbar} onClose={handleCloseSnackbar} />
        </Snackbar>
      )}
    </div>
  );
}

export default observer(EntriesTable);
