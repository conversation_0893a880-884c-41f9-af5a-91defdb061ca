import React, { forwardRef } from 'react';
import classNames from "classnames";
import { toJS } from 'mobx';

const PrintEntry = forwardRef(({ selectedGroup, addressString, shippingString, groupClassification }, ref) => {
    const { schoolName, ensembleName, director, contest, category, tracks } = selectedGroup;
    tracks.map(track=>{
        console.log('print entry', toJS(track))
    })
    return (
        <div ref={ref} className='print-container'>
            {/* Entry Information */}
            <div className="bg-white mb-4">
                <div className="bg-header px-4 py-3">
                    <h2 className="text-white font-semibold">Entry Information</h2>
                </div>
                <div className="p-4">
                    <div id="entry_info">
                        <p><strong>School Name:</strong> {schoolName}</p>
                        <p><strong>Ensemble Name:</strong> {ensembleName}</p>
                        <p><strong>Director:</strong> {director}</p>
                        <p><strong>Contest:</strong> {contest.name}</p>
                        <p><strong>Category:</strong> {category.name}</p>
                        <p><strong>{groupClassification.title}</strong> {groupClassification.classification}</p>
                        <p><strong>Contact Info:</strong></p>
                        <p><strong>Email:</strong>{selectedGroup.email}</p>
                        <p><strong>Phone:</strong> {selectedGroup.cellphone}</p>
                        {selectedGroup?.shippingAddress ? (
                            <>
                                <p><strong>Mailing Address:</strong>{addressString}</p>
                                <p><strong>Shipping Address:</strong> {shippingString}</p>
                            </>
                        ):
                            <p><strong>Mailing/Shipping Address:</strong>{addressString}</p>
                        }
                    </div>
                </div>
            </div>

            {/* Track Information */}
            {tracks.map((track, index) => (
                <div className="bg-white mb-4" key={index}>
                    <div className="bg-header px-4 py-3">
                        <h2 className="text-white font-semibold">Selection {index + 1} Info</h2>
                    </div>
                    <div className="p-4">
                        <p><strong>Title:</strong> {track?.title}</p>
                        <p><strong>Composer:</strong> {track?.composer}</p>
                        <p><strong>Venue:</strong> {track?.venue}</p>
                        <p><strong>Date Recorded:</strong> {new Date(track?.dateRecorded).toLocaleDateString()}</p>
                        <p><strong>Duration:</strong> {track?.duration}</p>
                        {track.soloists.length ? (
                            <div>
                                <strong>Soloists:</strong>
                                <table style={{ borderCollapse: 'collapse' }}>
                                    <thead>
                                        <tr>
                                            <th style={{ padding: '0 10px' }}>Name</th>
                                            <th style={{ padding: '0 10px' }}>Instrument</th>
                                            <th style={{ padding: '0 10px' }}>Time Stamp</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {track.soloists.map((soloist, index) => (
                                            <tr key={index}>
                                                <td style={{ padding: '0 10px' }}>{soloist.name}</td>
                                                <td style={{ padding: '0 10px' }}>{soloist.instrument}</td>
                                                <td style={{ padding: '0 10px' }}>{soloist?.timeStamp}</td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>

                        ) : null}
                    </div>
                </div>
            ))}
        </div>
    );
});

export default PrintEntry;
