import React, { useState, useEffect, useContext } from "react";
import { Tab, Tabs, Typography, Box } from "@mui/material";
import { AppProviderStore } from "../../AppStore";
import { observer } from "mobx-react";
import { toJS } from "mobx";
import EntriesTable from '../../Components/AdminViewComponents/EntriesTable'

function EntriesView() {
  const { AppStore } = useContext(AppProviderStore);
  const { categories, contests } = AppStore;
  const [selectedCategory, setCategory] = useState(categories[0]._id);
  const [classifications, setClassifications] = useState([{_id:'all', name:'All'}, ...categories[0].classifications])
  const [selectedClassification, setSelectedClassification] = useState('all')

  
  useEffect(()=>{
    setSelectedClassification()
    let category = categories.find(category => category._id === selectedCategory)
    setClassifications([{_id:'all', name:'All'},...category.classifications])  
    setSelectedClassification('all')
  },[selectedCategory])

  const handleCategoryChange = (event, category) => {
    setCategory(category);
  };

  const handleClassificationChange = (event, classification) => {
    console.log('handleClassificationChange', classification)
    setSelectedClassification(classification);
  };

  const renderTabs = () => {
    return categories.map((category, index) => {
      return <Tab key={index} value={category._id} label={category.name} />;
    });
  };
  const renderSubTabs = () =>{
    return classifications.map((classification,index) =>{
      return <Tab key={index} value={classification._id} label={classification.name} />
    })
  }
  return (
    <div className="w-full">
      <Tabs variant="scrollable" scrollButtons="auto" value={selectedCategory} onChange={handleCategoryChange}>
        {renderTabs()}
      </Tabs>
      <Tabs variant="scrollable" scrollButtons="auto" value={selectedClassification} onChange={handleClassificationChange}>
        {renderSubTabs()}
      </Tabs>
      <EntriesTable category={selectedCategory} classification={selectedClassification} />
    </div>
  );
}

export default observer(EntriesView);



