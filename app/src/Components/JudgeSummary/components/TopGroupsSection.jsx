import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Chip,
  Button,
  CircularProgress
} from '@mui/material';
import {
  Flag as FlagIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';

const TopGroupsSection = ({ 
  sortedGroups, 
  isAutoSelectLoading, 
  handleAutoSelectTopGroups 
}) => {
  return (
    <Paper sx={{ p: 3, mb: 4, borderRadius: 2, boxShadow: 2 }}>
      <Typography variant="h6" sx={{ mb: 2, color: '#3f51b5', display: 'flex', alignItems: 'center' }}>
        <FlagIcon sx={{ mr: 1 }} /> Top 50% Groups
      </Typography>

      <Typography variant="body2" sx={{ mb: 2 }}>
        Based on current scores, the following groups are in the top 50% and should move on to Phase 2.
        You can manually override these selections using the "Moves On" checkbox.
      </Typography>

      <Box sx={{ mb: 3 }}>
        {sortedGroups.filter(group => group.isTopScore).length > 0 ? (
          <Grid container spacing={1}>
            {sortedGroups
              .filter(group => group.isTopScore)
              .sort((a, b) => b.score - a.score)
              .map(group => (
                <Grid item xs={12} sm={6} md={4} key={group._id}>
                  <Box
                    sx={{
                      p: 1,
                      border: '1px solid #e0e0e0',
                      borderRadius: 1,
                      display: 'flex',
                      alignItems: 'center',
                      backgroundColor: group.movesOn ? 'rgba(63, 81, 181, 0.1)' : 'transparent',
                      '&:hover': {
                        backgroundColor: 'rgba(63, 81, 181, 0.05)'
                      }
                    }}
                  >
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                        Group: {group.maskedName}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Score: {group.score}
                      </Typography>
                    </Box>
                    {group.movesOn ? (
                      <Chip
                        size="small"
                        label="Selected"
                        sx={{ backgroundColor: '#3f51b5', color: 'white' }}
                      />
                    ) : (
                      <Chip
                        size="small"
                        label="Not Selected"
                        variant="outlined"
                        sx={{ borderColor: '#3f51b5', color: '#3f51b5' }}
                      />
                    )}
                  </Box>
                </Grid>
              ))
            }
          </Grid>
        ) : (
          <Typography variant="body1" sx={{ textAlign: 'center', color: 'text.secondary', fontStyle: 'italic' }}>
            No groups have been scored yet. Score groups to see the top 50%.
          </Typography>
        )}
      </Box>

      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="body2" color="textSecondary">
          {sortedGroups.filter(group => group.isTopScore).length} groups in top 50%
        </Typography>

        <Button
          variant="outlined"
          color="primary"
          size="small"
          onClick={() => handleAutoSelectTopGroups()}
          disabled={sortedGroups.filter(group => group.isTopScore).length === 0 || isAutoSelectLoading}
          startIcon={isAutoSelectLoading ? null : <CheckCircleIcon />}
        >
          {isAutoSelectLoading ? (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <CircularProgress size={16} sx={{ mr: 1 }} />
              Updating...
            </Box>
          ) : (
            'Auto-Select All Top Groups'
          )}
        </Button>
      </Box>
    </Paper>
  );
};

export default TopGroupsSection;
