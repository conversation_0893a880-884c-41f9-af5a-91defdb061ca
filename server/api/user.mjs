import User from "../models/user.mjs";
import Judge from "../models/judge.mjs";
import jwt from 'jsonwebtoken';
import sgMail from '@sendgrid/mail'

import { randomBytes } from 'crypto'

const secretKey = randomBytes(32).toString('hex');

async function getJudgeIntros(judges, classification) {
    console.log('getting judgeIntros')
    let intros = []
    // for(const judge of judges){
    //     console.log(judge)
    //     let judgeInfo = await Judge.findById(judge.judgeId)
    //     let intro = judgeInfo.judgeIntro ? judgeInfo.judgeIntro : null
    //     intros.push({name: judgeInfo.name, judgeIntro: intro})
    // }

    return intros
}

async function getJudgeIntrosByMemos(judges, category, classifications) {
    console.log('in this function', 'classifications: ', classifications, 'category: ', category);
    let judgeIntros = [];

    // Fetch judges information
    let judgesInfo = await Judge.find({ _id: { $in: judges } }).lean();

    // Loop through each classification to prepare judgeIntros
    if (!Array.isArray(classifications)) {
        console.log('single class');
        console.log(classifications);
        let intros = [];
        judgesInfo.forEach(judge => {
            intros.push({ intro: judge.judgeIntro, name: judge.name });
        });
        judgeIntros.push({
            classification: classifications.name,
            intros: intros
        });
    } else {
        classifications.forEach(classification => {
            console.log('abc', classification, classification.id.name);

            // Prepare object for the current classification
            let classificationObj = {
                classification: classification.id.name,
                intros: []
            };

            // Loop through each judge
            judgesInfo.forEach(judge => {
                // Check if the judge has the classification
                judge.assigned.forEach(assignment => {
                    // Skip this assignment if the category does not match
                    console.log('Judge: ', judge.name)
                    console.log('Assigned: ', assignment.category, 'Comparing: ', category._id)
                    console.log(assignment.category.toString() === category._id.toString())
                    if (assignment.category.toString() !== category._id.toString()) {
                        console.log('category doesnt match', assignment.category)
                        return;
                    }
                    console.log(judge.name, assignment)
                    assignment.classifications.forEach(judgeClassification => {
                        console.log('classifications', judgeClassification.id, classification.id._id);
                        console.log((judgeClassification.id.toString() === classification.id._id.toString()));

                        if (judgeClassification.id.toString() === classification.id._id.toString()) {
                            // Add the judgeIntro URL if a match is found
                            console.log('matched', judge.name);
                            classificationObj.intros.push({ intro: judge.judgeIntro, name: judge.name });
                        }
                    });
                });
            });

            // Add to the judgeIntros array
            judgeIntros.push(classificationObj);
            console.log(judgeIntros);
        });
    }

    return judgeIntros;
}

async function setGroups(groups) {
    let groupArray = []

    for (const group of groups) {
        console.log('going through groups')
        if (group.isArchived) continue;
        let judgeIntros = []
        if (group.classifications && group.classifications.length) {
            for (const classification of group.classifications) {
                if (classification.judgeScores.length) {
                    let intros = await getJudgeIntros(classification.judgeScores)
                    judgeIntros.push({
                        classification: classification.id.name,
                        intros: [...intros]
                    })
                    console.log('judgeIntros')
                    console.log(judgeIntros)
                }
            }
            if (judgeIntros.length === 0) {
                let judges = []
                for (const track of group.tracks) {
                    console.log('on track:', track)
                    console.log('hmm', track.memos?.audioMemos)
                    if (track.memos?.audioMemos && track.memos.audioMemos.length === 0) {
                        continue;
                    } else if (track.memos?.audioMemos) {
                        track.memos.audioMemos.map(memo => {
                            console.log('in here')
                            if (!judges.includes(memo.judge)) {
                                judges.push(memo.judge)
                            }
                        })
                    }
                }
                judgeIntros = await getJudgeIntrosByMemos(judges, group.category, group.classifications)
                console.log('hmm', judgeIntros)
            }
        } else {
            if (group.judgeScores.length) {
                let intros = await getJudgeIntros(group.judgeScores)
                judgeIntros.push({
                    classification: group.classification.name,
                    intros: [...intros]
                })
            }
            if (judgeIntros.length === 0) {
                let judges = []
                for (const track of group.tracks) {
                    console.log('on track:', track)
                    console.log('hmm', track.memos?.audioMemos)
                    if (track.memos?.audioMemos && track.memos.audioMemos.length === 0) {
                        continue;
                    } else if (track.memos?.audioMemos) {
                        track.memos.audioMemos.map(memo => {
                            console.log('in here')
                            if (!judges.includes(memo.judge)) {
                                judges.push(memo.judge)
                            }
                        })
                    }
                }
                judgeIntros = await getJudgeIntrosByMemos(judges, group.category, group.classification)
            }
        }
        groupArray.push({
            group: group,
            judgeIntros: judgeIntros
        })
    }

    return groupArray
}


export default async function (fastify, opts) {
    sgMail.setApiKey(process.env.SENDGRID_API_KEY)

    // Get all users
    fastify.get('/api/getAllUsers', async function (request, response) {
        try {
            const currentYear = new Date().getFullYear()
            let pipeline = [
                {
                    $lookup: {
                        from: "groups",
                        localField: "groups",
                        foreignField: "_id",
                        as: "allGroups"
                    }
                },
                { $unwind: "$allGroups" },
                {
                    $lookup: {
                        from: "contests",
                        localField: "allGroups.contest",
                        foreignField: "_id",
                        as: "allGroups.contest"
                    }
                },
                {
                    $lookup: {
                        from: "categories",
                        localField: "allGroups.category",
                        foreignField: "_id",
                        as: "allGroups.category"
                    }
                },
                {
                    $lookup: {
                        from: "classifications",
                        localField: "allGroups.classification",
                        foreignField: "_id",
                        as: "allGroups.classification"
                    }
                },
                {
                    $lookup: {
                        from: "classifications",
                        localField: "allGroups.classifications.id",
                        foreignField: "_id",
                        as: "allGroups.classifications"
                    }
                },
                // Separate currGroups and previousGroups
                {
                    $group: {
                        _id: "$_id",
                        email: { $first: "$email" },
                        isAdmin: { $first: "$isAdmin" },
                        lastLoggedIn: { $first: "$lastLoggedIn" },
                        currGroups: {
                            $push: {
                                $cond: {
                                    if: { $eq: ["$allGroups.year", currentYear] },
                                    then: {
                                        $mergeObjects: [
                                            "$allGroups",
                                            {
                                                "category": { $arrayElemAt: ["$allGroups.category.name", 0] },
                                                "contest": { $arrayElemAt: ["$allGroups.contest.name", 0] },
                                                "classification": { $arrayElemAt: ["$allGroups.classification.name", 0] },
                                                "classifications": "$allGroups.classifications.name"
                                            }
                                        ]
                                    },
                                    else: "$$REMOVE"
                                }
                            }
                        },
                        previousGroups: {
                            $push: {
                                $cond: {
                                    if: { $ne: ["$allGroups.year", currentYear] },
                                    then: {
                                        $mergeObjects: [
                                            "$allGroups",
                                            {
                                                "category": { $arrayElemAt: ["$allGroups.category.name", 0] },
                                                "contest": { $arrayElemAt: ["$allGroups.contest.name", 0] },
                                                "classification": { $arrayElemAt: ["$allGroups.classification.name", 0] },
                                                "classifications": "$allGroups.classifications.name"
                                            }
                                        ]
                                    },
                                    else: "$$REMOVE"
                                }
                            }
                        }
                    }
                }
            ]
            const users = await User.aggregate(pipeline);
            response.send(users)
        } catch (error) {
            response.status(500).send(error);
        }
    });

    // Get a user by ID
    fastify.get('/api/getUser/:id', async function (request, response) {
        const userId = request.params.id;
        try {
            const user = await User.findById(userId).populate({
                path: 'groups',
                populate: [
                    { path: "tracks" },
                    { path: 'classification', select: "name" },
                    { path: 'classifications.id', select: "name" },
                    { path: 'contest', select: "name" },
                    { path: 'category', select: 'name' }
                ]
            }).lean();
            let groupArray = await setGroups(user.groups)
            let returnUser = { ...user }
            delete returnUser.groups
            returnUser.groups = groupArray
            if (!user) {
                return response.status(404).send({ message: 'User not found' });
            }
            response.send(returnUser);
        } catch (error) {
            response.status(500).send(error);
        }
    });

    // Create a new user
    fastify.post('/api/createUser', async function (request, response) {
        const userData = request.body;
        const existingUser = await User.findOne({ email: userData.email })
        if (existingUser) {
            return response.status(400).send({ message: 'Email is already registered' });
        }
        try {
            const newUser = new User(userData);
            await newUser.save();
            response.send(newUser);
        } catch (error) {
            response.status(500).send(error);
        }
    });

    // Update a user by ID
    fastify.put('/api/updateUser/:id', async function (request, response) {
        const userId = request.params.id;
        const updatedUserData = request.body;
        try {
            const updatedUser = await User.findByIdAndUpdate(userId, updatedUserData, { new: true });
            if (!updatedUser) {
                return response.status(404).send({ message: 'User not found' });
            }
            response.send(updatedUser);
        } catch (error) {
            response.status(500).send(error);
        }
    });

    // Delete a user by ID
    fastify.delete('/api/deleteUser/:id', async function (request, response) {
        const userId = request.params.id;
        try {
            const deletedUser = await User.findByIdAndDelete(userId);
            if (!deletedUser) {
                return response.status(404).send({ message: 'User not found' });
            }
            response.send({ message: 'User deleted successfully' });
        } catch (error) {
            response.status(500).send(error);
        }
    });

    fastify.post('/api/login', async function (request, response) {
        let { email, password } = request.body;
        //email = email.toLowerCase();
        try {
            const user = await User.findOne({ email: { $regex: new RegExp('^' + email + '$', 'i') } });
            console.log('user found? : ')
            console.log(user);
            if (!user) {
                return response.status(401).send({ message: 'user' })
            } else {
                const validPassword = await user.comparePassword(password)
                if (!validPassword) {
                    return response.status(401).send({ message: 'password' })
                } else {
                    await User.findOneAndUpdate({ email }, { lastLoggedIn: new Date() });
                    // Get the updated user document
                    const updatedUser = await User.findOne({ email: { $regex: new RegExp('^' + email + '$', 'i') } }).populate({
                        path: 'groups',
                        populate: [
                            { path: "tracks" },
                            { path: 'classification', select: "name" },
                            { path: 'classifications.id', select: "name" },
                            { path: 'contest', select: "name" },
                            { path: 'category', select: 'name' }
                        ]
                    }).lean();
                    let groupArray = await setGroups(updatedUser.groups)
                    let returnUser = { ...updatedUser }
                    delete returnUser.groups
                    returnUser.groups = groupArray
                    const token = jwt.sign({ returnUser }, secretKey, { expiresIn: '24h' })
                    response.send(token);

                }
            }

        } catch (error) {
            response.status(500).send(error);
        }
    });

    fastify.post('/api/forgotPassword', async function (request, response) {
        const { email } = request.body;
        try {
            const user = await User.findOne({ email: { $regex: new RegExp('^' + email + '$', 'i') } });
            if (!user) {
                return response.status(404).send({ message: "User not found" })
            }
            const resetToken = jwt.sign({ userId: user._id }, secretKey, { expiresIn: '1h' });
            user.resetToken = resetToken;
            user.resetTokenExpiration = Date.now() + 3600000; // 1 hour in milliseconds
            let resetUrl = `https://app.foundationformusiceducation.org/reset-password?token=${resetToken}`
            await user.save();
            const msg = {
                to: email,
                from: { email: '<EMAIL>', name: "The Foundation for Music Education" },
                templateId: "d-44f3437f5fd1457ca96de600da99770b",
                dynamicTemplateData: {
                    resetUrl: resetUrl
                }

            }
            sgMail.send(msg)
                .then(() => {
                    console.log('Email sent successfully');
                })
                .catch((error) => {
                    console.error('Error sending email:', error);
                    console.dir(error, { depth: null })
                });
        } catch (error) {
            response.status(500).send(error)
        }
    })

    fastify.post('/api/resetPassword/', async function (request, response) {
        const { newPassword, resetToken } = request.body;
        try {
            const user = await User.findOne({ resetToken })
            if (!user || user.resetTokenExpiration < Date.now()) {
                return response.status(400).send({ message: "Invalid or expired token" })
            }
            user.password = newPassword
            user.resetToken = undefined
            user.resetTokenExpiration = undefined
            await user.save();
            response.send({ message: 'Password reset successfully' })
        } catch (error) {
            response.status(500).send(error)
        }
    })

}