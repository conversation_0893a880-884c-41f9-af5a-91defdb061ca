import { useState, useEffect, useContext, useRef } from "react";
import { useForm } from "react-hook-form"
import { observer } from 'mobx-react';



function RegisterGroup() {
  
  //get user profile info to be able to autopopulate some fields

  const { register, handleSubmit, formState: { errors, isSubmitting, isValid } } = useForm();
  const [isShippingDifferent, setShippingDifferent] = useState(false);

  const onSubmit = (data) => {
    // Handle form submission, e.g., send data to the server
    console.log(data);
  };

  const states = [
    "AK", "AL", "AR", "AZ", "CA", "CO", "CT", "DE", "FL", "GA", "HI", "IA", "ID",
    "IL", "IN", "KS", "KY", "LA", "MA", "MD", "ME", "MI", "MN", "MO", "MS", "MT",
    "NC", "ND", "NE", "NH", "NJ", "NM", "NV", "NY", "OH", "OK", "OR", "PA", "RI",
    "SC", "SD", "TN", "TX", "UT", "VA", "VT", "WA", "WI", "WV", "WY"
  ];

  return (
      <div className="container mx-auto p-8 max-w-screen-md">
        <form onSubmit={handleSubmit(onSubmit)} className="max-w-md mx-auto">
          {/* Line 1 */}
          <div className="flex mb-4">
            <select {...register("prefix", { required: true })} className="mr-2 p-2 rounded border" defaultValue="">
              <option value="" disabled hidden> Prefix</option>
              <option value="Mr">Mr</option>
              <option value="Ms">Ms</option>
              <option value="Mrs">Mrs</option>
              <option value="Mx">Mx</option>
            </select>
            <input
              {...register("name.first", { required: true })}
              placeholder="First Name"
              className="mr-2 p-2 rounded border"
            />
            <input
              {...register("name.last", { required: true })}
              placeholder="Last Name"
              className="flex-grow p-2 rounded border" // Adjusted style for last name input
            />
          </div>
          {errors.prefix && <span>This field is required</span>}
          {errors.name?.first && <span>This field is required</span>}
          {errors.name?.last && <span>This field is required</span>}

          {/* Line 2 */}
          <div className="flex mb-4">
            <input {...register("email", { required: true })} type="email" placeholder="Enter your email" className="mr-2 p-2 rounded border" />
            <input {...register("phone", { required: true })} placeholder="Phone" className="p-2 rounded border" />
          </div>
          {errors.email && <span>This field is required</span>}
          {errors.phone && <span>This field is required</span>}

          <input {...register("schoolName")} placeholder="School Name" className="mb-4 p-2 rounded border" />
          <div className="flex mb-4">
            <input {...register("mailingAddress.street")} placeholder="Street" className="mr-2 p-2 rounded border" />
            <input {...register("mailingAddress.city")} placeholder="City" className="p-2 rounded border" />
          </div>

          <div className="flex mb-4">
            <select {...register("mailingAddress.state")} className="mr-2 p-2 rounded border" defaultValue="">
              <option value="" disabled hidden>State</option>
              {states.map(state => (
                <option key={state} value={state}>{state}</option>
              ))}
            </select>
            <input {...register("mailingAddress.zip")} placeholder="ZIP" className="p-2 rounded border" />
          </div>

          <label className="text-white">
            <input type="checkbox" onChange={() => setShippingDifferent(!isShippingDifferent)} />
            Shipping address is different than mailing
          </label>

          {isShippingDifferent && (
            <>
              <div className="flex mb-4">
                <input {...register("shippingAddress.street")} placeholder="Shipping Street" className="mr-2 p-2 rounded border" />
                <input {...register("shippingAddress.city")} placeholder="Shipping City" className="p-2 rounded border" />
              </div>

              <div className="flex mb-4">
                <select {...register("shippingAddress.state")} className="mr-2 p-2 rounded border" defaultValue="">
                  <option value="" disabled hidden>State</option>
                  {states.map(state => (
                    <option key={state} value={state}>{state}</option>
                  ))}
                </select>
                <input {...register("shippingAddress.zip")} placeholder="Shipping ZIP" className="p-2 rounded border" />
              </div>
            </>
          )}
          <br/>
          {/* Submit button */}
          <button type="submit" className={`bg-green-500 text-white p-2 rounded-3xl ${!isValid || isSubmitting ? 'cursor-not-allowed opacity-50 ' : ''}`}>
            Register
          </button>
        </form>
      </div>
  );

}
export default observer(RegisterGroup);

