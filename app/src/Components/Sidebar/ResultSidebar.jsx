import { observer } from 'mobx-react';
import classNames from "classnames";
import { useContext } from 'react';
import { AppProviderStore } from '../../AppStore';
import { Link } from "react-router-dom";

const ResultSidebar = ({ menuOptions, handleSelect, selectedOption, selectedYear, handleYearChange }) => {
  const { AppStore } = useContext(AppProviderStore);

  // Generate year options from 2023 to the current year
  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: currentYear - 2023 + 1 }, (_, i) => currentYear - i);
  console.log('resultsidebar', menuOptions)
  return (
    <div className="w-[270px] pt-8 min-h-full bg-sidebar relative flex flex-col">
      <div className="px-5">
        <label htmlFor="year-select" className="text-white font-semibold text-lg">Select Year:</label>
        <select
          id="year-select"
          className="w-full p-3 mt-2 bg-white text-gray-700 rounded-lg shadow-md focus:outline-none focus:ring-2 focus:ring-green-500"
          value={selectedYear}
          onChange={handleYearChange}
        >
          {years.map(year => (
            <option key={year} value={year}>
              {year}
            </option>
          ))}
        </select>
      </div>

      <div className="flex-1 overflow-y-auto mt-5 px-5">
        <ul>
          {menuOptions.map((option, index) => (
            <li
              key={`${index}_${option.title}`}
              onClick={() => handleSelect(option)}
              className={classNames(
                "group flex items-center w-full gap-3 font-semibold p-4 mb-4 rounded-md duration-300",
                "hover:bg-[#3b8c6e] hover:text-white",
                selectedOption?.title === option.title
                  ? "bg-sidebarListItem text-white"
                  : "bg-white text-grayText"
              )}
            >
              {option.title}
            </li>
          ))}
        </ul>
      </div>

      <div className="sticky bottom-0 p-5 bg-sidebar bg-opacity-90">
        {AppStore.user ? (
          <Link to="/account">
            <button className="w-full p-3 bg-iconGreen text-white font-semibold rounded-lg shadow-md hover:bg-iconGreenDark duration-300">
              Go Back
            </button>
          </Link>
        ) : (
          <Link to="/login">
            <button className="w-full p-3 bg-iconGreen text-white font-semibold rounded-lg shadow-md hover:bg-iconGreenDark duration-300">
              Login
            </button>
          </Link>
        )}
      </div>
    </div>
  );
};

export default observer(ResultSidebar);
