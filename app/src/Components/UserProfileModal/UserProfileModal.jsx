import React, { useState, useContext } from 'react';
import { Modal, <PERSON>, Typography, TextField, Button, IconButton } from '@mui/material';
import { Close } from '@mui/icons-material';
import { AppProviderStore } from '../../AppStore';
import axios from 'axios';
import Swal from 'sweetalert2';

const UserProfileModal = ({ open, handleClose }) => {
  const { AppStore } = useContext(AppProviderStore);
  const { user, setUser } = AppStore;
  
  const [email, setEmail] = useState(user?.email || '');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleEmailChange = (e) => {
    setEmail(e.target.value);
  };

  const handleSubmit = async () => {
    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      Swal.fire({
        icon: 'error',
        title: 'Invalid Email',
        text: 'Please enter a valid email address',
      });
      return;
    }

    setIsSubmitting(true);
    try {
      // Call API to update user email
      const response = await axios.put(`/api/updateUser/${user._id}`, {
        email: email
      });
      
      if (response.data) {
        // Update the user in the AppStore
        const updatedUser = { ...user, email: email };
        setUser(updatedUser);
        
        Swal.fire({
          icon: 'success',
          title: 'Success!',
          text: 'Your email has been updated successfully',
        });
        
        handleClose();
      }
    } catch (error) {
      console.error('Error updating email:', error);
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'There was an error updating your email. Please try again later.',
        html: 'Please contact <a href="mailto:<EMAIL>"><EMAIL></a> if problem persists',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal
      open={open}
      onClose={handleClose}
      aria-labelledby="user-profile-modal-title"
    >
      <Box sx={{ 
        position: 'absolute', 
        top: '50%', 
        left: '50%', 
        transform: 'translate(-50%, -50%)', 
        width: 400, 
        bgcolor: 'background.paper', 
        boxShadow: 24, 
        p: 4,
        borderRadius: 2
      }}>
        <Typography id="user-profile-modal-title" variant="h6" component="h2" sx={{ mb: 2 }}>
          Manage Your Profile
        </Typography>
        <IconButton
          aria-label="close"
          onClick={handleClose}
          sx={{ position: 'absolute', top: 10, right: 10, color: 'grey.500' }}
        >
          <Close />
        </IconButton>
        
        <Typography variant="body1" sx={{ mb: 1 }}>
          Update your email address:
        </Typography>
        
        <TextField
          fullWidth
          label="Email"
          variant="outlined"
          value={email}
          onChange={handleEmailChange}
          margin="normal"
          type="email"
          sx={{ mb: 3 }}
        />
        
        <Button
          variant="contained"
          onClick={handleSubmit}
          disabled={isSubmitting}
          sx={{
            backgroundColor: '#00773d', // iconGreen color
            '&:hover': {
              backgroundColor: '#357e63' // darker shade for hover
            }
          }}
        >
          {isSubmitting ? 'Updating...' : 'Update Email'}
        </Button>
      </Box>
    </Modal>
  );
};

export default UserProfileModal;
