import { useEffect, useState, useContext } from "react";
import Review from '../Components/GroupViewComponents/Review'
import JudgeVideos from '../Components/GroupViewComponents/JudgeVideos'
import DebugBreadcrumb from "../Components/TestComponents/DebugBreadcrumb";
import { observer } from 'mobx-react';
import { AppProviderStore } from "../AppStore";
import { toJS } from "mobx";

function GroupView({ group }) {
    console.log('group view')
    // console.log('work view', toJS(group))
    const { AppStore } = useContext(AppProviderStore)
    console.log('group view', toJS(AppStore.selectedGroup))
    // console.log('selected', toJS(AppStore.menuSelected))
    return (
        <div>
            <DebugBreadcrumb breadcrumbs={"Components->Views->GroupView"} />
            {AppStore?.selectedGroup ? <Review /> : null}
        </div>
    );
}

export default observer(GroupView);