import React, { useState, useRef } from 'react';
import { PlayArrow, Pause } from '@mui/icons-material';
import { IconButton } from '@mui/material';




function AudioPlayer(props){
    const [isPlaying, setIsPlaying] = useState(false);
    const audioRef = useRef(null);
    const togglePlay = () => {
        if (isPlaying) {
          audioRef.current.pause();
        } else {
          audioRef.current.play();
        }
        setIsPlaying(!isPlaying);
      };

    if(props.value === null){
        return null;
    }
    return(
        <>
        <IconButton onClick={togglePlay}>
          {isPlaying ? <Pause /> : <PlayArrow />}
        </IconButton>
        <audio ref={audioRef} src={props.value} />
      </>
    )
}

export default AudioPlayer