import { useFormContext } from "react-hook-form";
import {
  TextField,
  Typography,
  Box
} from "@mui/material";

function EnsembleStep({ groupNumber }) {
  const {
    register,
    formState: { errors }
  } = useFormContext();

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
      <Typography variant="h6" gutterBottom>
        Ensemble & School Information
      </Typography>
      
      <Typography variant="body1" gutterBottom>
        <strong>Ensemble Name - Do not abbreviate. Exactly as you wish for it in
        publication. ie: Lamar High School Wind Ensemble.</strong>
      </Typography>
      
      <TextField
        {...register(`group_${groupNumber}.ensembleName`, { required: true })}
        required
        label="Ensemble Name"
        fullWidth
        error={!!errors?.[`group_${groupNumber}`]?.ensembleName}
        helperText={errors?.[`group_${groupNumber}`]?.ensembleName?.message}
      />
      
      <TextField
        {...register(`group_${groupNumber}.schoolName`, { required: true })}
        required
        label="School Name"
        fullWidth
        error={!!errors?.[`group_${groupNumber}`]?.schoolName}
        helperText={errors?.[`group_${groupNumber}`]?.schoolName?.message}
      />
      
      <TextField
        {...register(`group_${groupNumber}.schoolEnrollment`, { required: true })}
        required
        label="School Enrollment Size"
        fullWidth
        type="number"
        error={!!errors?.[`group_${groupNumber}`]?.schoolEnrollment}
        helperText={errors?.[`group_${groupNumber}`]?.schoolEnrollment?.message}
      />
    </Box>
  );
}

export default EnsembleStep;
