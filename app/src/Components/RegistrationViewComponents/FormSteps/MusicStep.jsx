import { useState, useEffect } from "react";
import { useF<PERSON><PERSON>ontex<PERSON>, Controller } from "react-hook-form";
import {
  TextField,
  Button,
  IconButton,
  Tooltip,
  FormLabel,
  Typography,
  Box,
  Paper,
  Divider
} from "@mui/material";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { MuiFileInput } from "mui-file-input";
import AddIcon from "@mui/icons-material/Add";
import CancelIcon from "@mui/icons-material/Cancel";
import AttachFileIcon from "@mui/icons-material/AttachFile";

function MusicStep({ groupNumber }) {
  const {
    register,
    unregister,
    watch,
    control,
    formState: { errors }
  } = useFormContext();

  const [soloists, setSoloists] = useState(Array.from({ length: 3 }, () => []));

  const selectedContest = watch(`group_${groupNumber}.contest`, "");
  const selectedCategory = watch(`group_${groupNumber}.category`, "");
  const selectedClassification = watch(`group_${groupNumber}.classification`, "");

  // Get the current tracks from the form
  const tracks = watch(`group_${groupNumber}.tracks`);

  // Initialize soloists state based on form data
  useEffect(() => {
    try {
      if (tracks && Array.isArray(tracks) && tracks.length > 0) {
        const updatedSoloists = [...soloists];

        // For each track, check if it has soloists and update the state
        tracks.forEach((track, trackIndex) => {
          if (track && track.soloists && Array.isArray(track.soloists) && track.soloists.length > 0) {
            // Create an array of soloist objects with IDs
            updatedSoloists[trackIndex] = track.soloists.map((_, index) => ({ id: index }));
          }
        });

        setSoloists(updatedSoloists);
      }
    } catch (error) {
      console.error("Error initializing soloists:", error);
    }
  }, [tracks]);

  const addSoloist = (trackId) => {
    setSoloists((prevSoloists) => {
      const newSoloistIndex = prevSoloists[trackId].length;
      const updatedSoloists = [...prevSoloists];
      updatedSoloists[trackId] = [...updatedSoloists[trackId], { id: newSoloistIndex }];
      return updatedSoloists;
    });
  };

  const removeSoloist = (trackId, soloistIndex) => {
    // First, get the current values of all soloists for this track
    const updatedTrackSoloists = soloists[trackId].filter((_, index) => index !== soloistIndex);

    // Unregister all soloist fields for this track to avoid validation issues with removed fields
    for (let i = 0; i < soloists[trackId].length; i++) {
      unregister([
        `group_${groupNumber}.tracks.${trackId}.soloists.${i}.name`,
        `group_${groupNumber}.tracks.${trackId}.soloists.${i}.instrument`,
        `group_${groupNumber}.tracks.${trackId}.soloists.${i}.timeStamp`,
      ]);
    }

    // Update the soloists state
    setSoloists((prevSoloists) => {
      const updatedSoloists = [...prevSoloists];
      updatedSoloists[trackId] = updatedTrackSoloists;
      return updatedSoloists;
    });
  };

  const soloistSection = (trackIndex) => {
    return (
      <>
        <Typography variant="subtitle2" sx={{ mt: 2, mb: 2, fontWeight: 'bold' }}>Soloists</Typography>
        {soloists[trackIndex].map(({ id }, index) => (
          <Paper
            key={id}
            elevation={1}
            sx={{
              p: 2,
              mb: 2,
              position: 'relative',
              borderLeft: '4px solid #008544',
              '&:hover': {
                boxShadow: 3
              }
            }}
          >
            <Box sx={{ position: 'absolute', top: 8, right: 8 }}>
              <Tooltip title="Remove Soloist">
                <IconButton
                  aria-label="Remove Soloist"
                  size="small"
                  color="error"
                  onClick={() => removeSoloist(trackIndex, index)}
                >
                  <CancelIcon />
                </IconButton>
              </Tooltip>
            </Box>

            <Typography variant="subtitle2" sx={{ mb: 1 }}>
              Soloist #{index + 1}
            </Typography>

            <Box sx={{
              display: 'grid',
              gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr 1fr' },
              gap: 2
            }}>
              <TextField
                {...register(
                  `group_${groupNumber}.tracks.${trackIndex}.soloists.${index}.name`,
                  { required: true }
                )}
                required
                label="Soloist Name"
                fullWidth
                error={!!errors?.[`group_${groupNumber}`]?.tracks?.[trackIndex]?.soloists?.[index]?.name}
                helperText={errors?.[`group_${groupNumber}`]?.tracks?.[trackIndex]?.soloists?.[index]?.name?.message}
              />
              <TextField
                {...register(
                  `group_${groupNumber}.tracks.${trackIndex}.soloists.${index}.instrument`,
                  { required: true }
                )}
                required
                label="Instrument"
                fullWidth
                error={!!errors?.[`group_${groupNumber}`]?.tracks?.[trackIndex]?.soloists?.[index]?.instrument}
                helperText={errors?.[`group_${groupNumber}`]?.tracks?.[trackIndex]?.soloists?.[index]?.instrument?.message}
              />
              <TextField
                {...register(
                  `group_${groupNumber}.tracks.${trackIndex}.soloists.${index}.timeStamp`
                )}
                label="Time Stamp"
                fullWidth
              />
            </Box>
          </Paper>
        ))}
        {soloists[trackIndex].length === 0 && (
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2, fontStyle: 'italic' }}>
            No soloists added yet. Click "Add Soloist" to add one.
          </Typography>
        )}
      </>
    );
  };

  const musicSelections = () => {
    let numberOfSelections = 2;
    let isOptional = false;
    let choralId = "64345656dd98890aad8cef2d";
    let jazzId = "643456dbdd98890aad8cef2e";
    let percussionId = "643456dbdd98890aad8cef30";
    let citationId = "64345c8fdd98890aad8cef3d";
    let hasSoloist = false;

    if (selectedContest !== citationId) {
      if (selectedCategory !== choralId && selectedCategory !== percussionId) {
        hasSoloist = true;
      }
    }

    if (selectedCategory === choralId) {
      numberOfSelections = 3;
    } else if (
      selectedCategory === jazzId ||
      selectedCategory === percussionId
    ) {
      numberOfSelections = 3;
      isOptional = true;
    }

    const musicFields = [];

    for (let i = 0; i < numberOfSelections; i++) {
      let formlabel = `Selection #${i + 1}`;
      if (i === 1) {
        formlabel += ` (If you are a National Winner this will be included in the compilation)`;
      }
      // Make the third track optional for jazz and percussion categories
      const isThirdTrackOptional = isOptional && i === 2;
      if (isThirdTrackOptional) {
        formlabel += ` (Optional)`;
      }

      musicFields.push(
        <Box key={`selection-${i}`} sx={{ mb: 4 }}>
          <FormLabel component="legend" sx={{ fontWeight: 'bold', display: 'block', mb: 2 }}>
            {formlabel}
          </FormLabel>

          <Box sx={{
            display: 'grid',
            gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' },
            gap: 2
          }}>
            <TextField
              {...register(`group_${groupNumber}.tracks.${i}.title`, { required: !isThirdTrackOptional })}
              required={!isThirdTrackOptional}
              label="Title"
              fullWidth
              error={!!errors?.[`group_${groupNumber}`]?.tracks?.[i]?.title}
              helperText={errors?.[`group_${groupNumber}`]?.tracks?.[i]?.title?.message}
            />

            <TextField
              {...register(`group_${groupNumber}.tracks.${i}.composer`, { required: !isThirdTrackOptional })}
              required={!isThirdTrackOptional}
              label="Composer/Arranger"
              fullWidth
              error={!!errors?.[`group_${groupNumber}`]?.tracks?.[i]?.composer}
              helperText={errors?.[`group_${groupNumber}`]?.tracks?.[i]?.composer?.message}
            />

            <TextField
              {...register(`group_${groupNumber}.tracks.${i}.duration`, { required: !isThirdTrackOptional })}
              required={!isThirdTrackOptional}
              label="Duration (mm:ss)"
              fullWidth
              error={!!errors?.[`group_${groupNumber}`]?.tracks?.[i]?.duration}
              helperText={errors?.[`group_${groupNumber}`]?.tracks?.[i]?.duration?.message}
            />

            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <Controller
                control={control}
                name={`group_${groupNumber}.tracks.${i}.dateRecorded`}
                render={({ field, fieldState }) => (
                  <DatePicker
                    label="Date of Recording"
                    value={field.value || null}
                    onChange={(date) => field.onChange(date)}
                    TextFieldComponent={(params) => (
                      <TextField
                        {...params}
                        fullWidth
                        error={!!fieldState.error}
                        helperText={fieldState.error?.message}
                      />
                    )}
                  />
                )}
              />
            </LocalizationProvider>

            <TextField
              {...register(`group_${groupNumber}.tracks.${i}.venue`, { required: !isThirdTrackOptional })}
              required={!isThirdTrackOptional}
              label="Recording Event/Venue"
              fullWidth
              error={!!errors?.[`group_${groupNumber}`]?.tracks?.[i]?.venue}
              helperText={errors?.[`group_${groupNumber}`]?.tracks?.[i]?.venue?.message}
            />

            <Controller
              name={`group_${groupNumber}.tracks.${i}.file`}
              control={control}
              rules={{ required: !isThirdTrackOptional ? "Audio file is required" : false }}
              render={({ field, fieldState }) => (
                <MuiFileInput
                  required={!isThirdTrackOptional}
                  {...field}
                  fullWidth
                  InputProps={{
                    inputProps: {
                      accept: ".wav, .mp3",
                    },
                    endAdornment: <AttachFileIcon />,
                  }}
                  label="Audio File Upload (.wav, .mp3)"
                  helperText={fieldState.error ? fieldState.error.message : ""}
                  error={!!fieldState.error}
                />
              )}
            />
          </Box>

          {selectedClassification && hasSoloist && (
            <Box sx={{ mt: 4 }}>
              <Divider sx={{ mb: 2 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 'bold', color: '#008544' }}>
                  Soloists Information
                </Typography>
              </Divider>
              {soloistSection(i)}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 2 }}>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => addSoloist(i)}
                  sx={{
                    bgcolor: '#008544',
                    '&:hover': { bgcolor: '#006834' },
                    borderRadius: '20px',
                    px: 3
                  }}
                >
                  Add Soloist
                </Button>
                <Typography variant="caption" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                  *Add a time stamp to help identify multiple soloists of the same instrument
                </Typography>
              </Box>
            </Box>
          )}
        </Box>
      );
    }

    return musicFields;
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column' }}>
      <Typography variant="h6" gutterBottom>
        Music Selection Information
      </Typography>

      {selectedClassification ? (
        musicSelections()
      ) : (
        <Typography variant="body1" color="error">
          Please select a classification in the previous step to continue.
        </Typography>
      )}
    </Box>
  );
}

export default MusicStep;
