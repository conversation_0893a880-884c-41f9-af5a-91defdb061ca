import mongoose, { SchemaType } from "mongoose";
const { Schema, model } = mongoose;

const judgeSchema = new Schema({
  isActive: {
    type: Boolean,
    default: true,
  },
  name: {
    type: String,
    required: true,
  },
  email: {
    type: String,
    required: true,
  },
  judgeIntro: {
    type: String,
  },
  assigned: [{
    category: {
      type: Schema.Types.ObjectId,
      ref: "Categories"
    },
    classifications: [{
      id: {
        type: Schema.Types.ObjectId,
        ref: "Classifications"
      },
      groups: [{
        type: Schema.Types.ObjectId,
        ref: "Groups"
      }],
      phase: Number
    }]
  }],
});

const Judge = model("Judges", judgeSchema);
export default Judge;
