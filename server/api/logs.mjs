import Logs from "../models/logs.mjs";

export default async function (fastify, opts) {

    fastify.post(`/api/log`, async function (request, response) {
        let { event } = request.body
        try {
            let newLog = new Logs({ event: event })
            await newLog.save()
            response.code(200).send("Log event added")

        }catch(err){
            console.error("Error creating a log event")
        }
    })
}