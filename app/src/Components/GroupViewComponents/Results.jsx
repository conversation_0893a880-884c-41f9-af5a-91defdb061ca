import { useState, useEffect, useContext, useRef } from "react";
import { AppProviderStore } from "../../AppStore";
import classNames from "classnames";
import { observer } from "mobx-react";
import AudioPlayer from "../WorkViewComponents/AudioPlayer";
import { toJS } from "mobx";
import { FaDownload } from "react-icons/fa";


function Results(){
    const [currentVideoIndex, setCurrentVideoIndex] = useState({});
    const [selectedClassification, setSelectedClassification] = useState(null);

    const videoRef = useRef(null)
    const { AppStore } = useContext(AppProviderStore);
    const { selectedGroup, currentTrack, setTrack, trackList, judgeIntros,competitonState } = AppStore;
    console.log('Review---', toJS(AppStore.groups), toJS(judgeIntros))
    console.log(toJS(selectedGroup))
    const initButtons =
        trackList.length > 1
            ? {
                prev: false,
                next: true,
            }
            : false;
    const [toggleButtons, setButtons] = useState(initButtons);



    useEffect(() => {
        if (videoRef.current) {
            videoRef.current.load()
        }
    }, [currentVideoIndex])

    useEffect(() => {
        setSelectedClassification(null)
        if (!selectedGroup || !judgeIntros || judgeIntros.length === 0) {
            return; // Exit early if conditions are not met
        }

        console.log('useEffect, [selectedGroup]')
        setCurrentVideoIndex(prevIndexes => ({
            ...prevIndexes,
            [selectedGroup.classification]: 0
        }));
        console.log('selectedGroup changed to:', toJS(selectedGroup), toJS(judgeIntros))
        console.log('Debugging setSelectedClassification', 'do a thing')
        console.log('Debugging setSelectedClassification', toJS(judgeIntros[0]?.classification))

        setSelectedClassification(judgeIntros[0]?.classification || null);
        if (videoRef.current) {
            videoRef.current.load();
        }

        setButtons(initButtons)
    }, [selectedGroup, judgeIntros])

    const changeTrack = async (direction) => {
        console.log(direction);
        let currIndex = trackList.indexOf(currentTrack);
        let shiftIndex = currIndex + direction;
        let maxIndex = trackList.length - 1;

        let buttons = { ...toggleButtons };

        if (shiftIndex > 0) {
            buttons.prev = true;
        } else {
            buttons.prev = false;
        }

        if (shiftIndex < maxIndex) {
            buttons.next = true;
        } else {
            buttons.next = false;
        }
        console.log(trackList[shiftIndex]);
        setTrack(trackList[shiftIndex]);
        setButtons({ ...buttons });
        console.log("track changed", currentTrack);
    };


    function renderClassificationIntros(classificationGroup) {
        console.log("Debugging - renderClassificationIntros - judgeIntros", toJS(judgeIntros[0].classification), '| selectedClassification', selectedClassification)
        console.log('renderClassificationIntros', toJS(classificationGroup))
        const currentIndex = currentVideoIndex[classificationGroup.classification] || 0;
        const currentIntro = classificationGroup.intros[currentIndex];
        console.log(toJS(currentIntro))
        const videoId = `judge_intro_video_${classificationGroup.classification}_${currentIndex}`;

        return (
            <div key={classificationGroup.classification}>
                {classificationGroup.intros.length > 1 ? (
                    <div className="flex flex-wrap gap-2 mb-4">
                        {classificationGroup.intros.map((intro, tabIndex) => (
                            <button
                                key={tabIndex}
                                onClick={() => {
                                    setCurrentVideoIndex({
                                        ...currentVideoIndex,
                                        [classificationGroup.classification]: tabIndex,
                                    });
                                }}
                                className={currentIndex === tabIndex ? 'active-tab-class' : 'inactive-tab-class'}
                            >
                                {intro.name || `Intro ${tabIndex + 1}`}
                            </button>
                        ))}
                    </div>
                ) : (
                    <button className=" block mb-2 active-tab-class single-tab">
                        {currentIntro.name}
                    </button>
                )}

                <div className="flex justify-center w-full">
                    <video ref={videoRef} id={videoId} width="320" height="240" controls>
                        <source src={currentIntro.judgeIntro} type="video/webm" />
                        Your browser does not support the video tag.
                    </video>
                </div>
            </div>
        );
    }

    function renderDownloadCodes() {
        if (selectedGroup.classifications && selectedGroup.classifications.length > 1) {
            console.log('bacon', toJS(selectedGroup.classifications));
            return (
                <>
                    <div className="flex flex-wrap gap-2 mb-4">
                        {selectedGroup.classifications.map(classification => (
                            <button
                                key={classification.id.$oid}
                                onClick={() => setSelectedClassification(classification.id.name)}
                                className={selectedClassification === classification.id.name ? 'active-tab-class' : 'inactive-tab-class'}
                            >
                                {classification.id.name}
                            </button>
                        ))}
                    </div>
                    {selectedClassification && selectedGroup.classifications.some(classification => classification.id.name === selectedClassification) && (
                        <p className="font-semibold">
                            Download Code: {selectedGroup.classifications.find(classification => classification.id.name === selectedClassification).downloadCode}
                        </p>
                    )}
                </>
            );
        } else if (selectedGroup.downloadCode) {
            return (
                <p className="font-semibold">
                    Download Code: {selectedGroup.downloadCode}
                </p>
            );
        } else {
            return <p className="text-iconGreen font-semibold">No download code available</p>;
        }
    }

    function downloadAudio(src,index) {
        const a = document.createElement('a');
        let memoNum = index+1
        let fileName = `${currentTrack.title}_memo${memoNum}.mp3`
        a.href = src;

        a.setAttribute('download', fileName); // This prompts the download
        a.setAttribute('type', 'audio/mp3'); // Make sure to set the correct MIME type
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    }

    return (
        <>
            <div className={classNames("grid grid-cols-[1.5fr_1fr] gap-4")}>
                {/* Memos Section */}
                <div className="bg-white">
                    <div className="bg-header px-4 py-3">
                        <h2 className="text-white font-semibold">Memos</h2>
                    </div>
                    <div className="p-4">
                        <div id="audio_memos">
                            {AppStore.memos.audioMemos && AppStore.memos.audioMemos.length > 0 ? (
                                <div className="bg-[#d7d7d7] p-4 mb-4 gap-y-3 flex flex-col">
                                    {AppStore.memos.audioMemos.map((src, index) => {
                                        let memoid = `memo_${index}`;
                                        return (
                                            <div
                                                key={index}
                                                className={classNames(
                                                    "px-4 py-3 rounded-md bg-white flex justify-between items-center gap-4"
                                                )}
                                            >
                                                <div className="flex gap-4 items-center">
                                                    <label key={memoid} className="text-grayText font-semibold">
                                                        Memo {index + 1} @{" "}
                                                    </label>
                                                    <audio id={memoid} src={src} controls />
                                                    <FaDownload
                                                        onClick={() => downloadAudio(src,index)}
                                                        className="text-[#3B8C6E] cursor-pointer hover:opacity-80"
                                                        size={24}  // optional, adjust the size as needed
                                                    />

                                                </div>
                                            </div>
                                        );
                                    })}
                                </div>
                            ) : (
                                <p className="text-iconGreen font-semibold px-4 py-1 w-fit mb-4 rounded-3xl border border-1 border-iconGreen">
                                    No memos recorded
                                </p>
                            )}
                        </div>
                    </div>
                </div>

                {/* Judge Intros Section */}
                <div className="bg-white">
                    <div className="bg-header px-4 py-3">
                        <h2 className="text-white font-semibold">Judge Intros</h2>
                    </div>
                    <div className="p-4">
                        {judgeIntros.length > 0 ? (
                            <>
                                {judgeIntros.length > 1 && (
                                    <div className="flex flex-wrap gap-2 mb-4">
                                        {judgeIntros.map(classificationGroup => (
                                            <button
                                                key={classificationGroup.classification}
                                                onClick={() => setSelectedClassification(classificationGroup.classification)}
                                                className={selectedClassification === classificationGroup.classification ? 'active-tab-class' : 'inactive-tab-class'}
                                            >
                                                {classificationGroup.classification}
                                            </button>
                                        ))}
                                    </div>
                                )}
                                {selectedClassification && judgeIntros.some(group => group.classification === selectedClassification) && renderClassificationIntros(judgeIntros.find(group => group.classification === selectedClassification))}
                            </>
                        ) : (
                            <p className="text-iconGreen font-semibold px-4 py-1 w-fit mb-4 rounded-3xl border border-1 border-iconGreen">
                                No judge intros added
                            </p>
                        )}
                    </div>
                </div>
            </div>

            <div className={classNames("grid grid-cols-[1.5fr_1fr] gap-4")}>

                <div className="bg-white">
                    <div className="bg-header px-4 py-3">
                        <h2 className="text-white font-semibold">Notes</h2>
                    </div>
                    <div className="p-4">
                        <div className={classNames("bg-listBorderColor  rounded-md p-3 mb-4")}>
                            <p className="text-iconGreen font-semibold">Performance Notes: </p>
                            <div className="flex flex-row justify-start gap-4 mt-2 mb-2 items-center">
                                <textarea disabled rows={3} class="peer block min-h-[auto] w-full rounded border-0 bg-white px-3 py-[0.32rem] leading-[1.6] outline-none transition-all duration-200 ease-linear focus:placeholder:opacity-100 data-[te-input-state-active]:placeholder:opacity-100 motion-reduce:transition-none dark:text-neutral-200 dark:placeholder:text-neutral-200 [&:not([data-te-input-placeholder-active])]:placeholder:opacity-0 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                    value={AppStore.memos.notes}
                                />
                            </div>
                        </div>
                        <div className={classNames("bg-listBorderColor  rounded-md p-3 mb-4")}>
                            <p className="text-iconGreen font-semibold">Additional Notes: </p>
                            <div className="flex flex-row justify-start gap-4 mt-2 mb-2 items-center">
                                <textarea disabled rows={3} class="peer block min-h-[auto] w-full rounded border-0 bg-white px-3 py-[0.32rem] leading-[1.6] outline-none transition-all duration-200 ease-linear focus:placeholder:opacity-100 data-[te-input-state-active]:placeholder:opacity-100 motion-reduce:transition-none dark:text-neutral-200 dark:placeholder:text-neutral-200 [&:not([data-te-input-placeholder-active])]:placeholder:opacity-0 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                    value={AppStore.memos.soloistNotes}
                                />
                            </div>
                        </div>
                    </div>
                </div>

                <div className="bg-white">
                    <div className="bg-header px-4 py-3">
                        <h2 className="text-white font-semibold">{selectedGroup.downloadCode ? "Download Code" : "Download Codes"}</h2>
                    </div>
                    <div className="p-4">
                        {renderDownloadCodes()}
                    </div>
                </div>

            </div>
        </>
    )
}
export default observer(Results)