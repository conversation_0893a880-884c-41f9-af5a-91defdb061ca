import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Controller } from "react-hook-form";
import {
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  Typography,
  Box,
  FormHelperText
} from "@mui/material";

function ContactStep({ groupNumber }) {
  const {
    register,
    watch,
    control,
    formState: { errors }
  } = useFormContext();

  const [isShippingDifferent, setShippingDifferent] = useState(false);

  // Watch for shipping address to determine if it's different from mailing
  const shippingAddress = watch(`group_${groupNumber}.shippingAddress`);

  // Initialize shipping different state based on form data
  useEffect(() => {
    try {
      if (shippingAddress && typeof shippingAddress === 'object' &&
          (shippingAddress.street || shippingAddress.city || shippingAddress.state || shippingAddress.zip)) {
        setShippingDifferent(true);
      }
    } catch (error) {
      console.error("Error checking shipping address:", error);
    }
  }, [shippingAddress]);

  const states = [
    "AK", "AL", "AR", "AZ", "CA", "CO", "CT", "DE", "FL", "GA",
    "IA", "HI", "ID", "IL", "IN", "KS", "KY", "LA", "MA", "MD",
    "ME", "MI", "MN", "MO", "MS", "MT", "NC", "ND", "NE", "NH",
    "NJ", "NM", "NV", "NY", "OH", "OK", "OR", "PA", "RI", "SC",
    "SD", "TN", "TX", "UT", "VA", "VT", "WA", "WI", "WV", "WY",
  ];

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
      <Typography variant="h6" gutterBottom>
        Contact Information
      </Typography>

      <TextField
        {...register(`group_${groupNumber}.cellphone`, { required: true })}
        required
        label="Cell Phone"
        fullWidth
        type="tel"
        error={!!errors?.[`group_${groupNumber}`]?.cellphone}
        helperText={errors?.[`group_${groupNumber}`]?.cellphone?.message}
      />

      <Typography variant="subtitle1" gutterBottom>
        Mailing Address:
      </Typography>

      <Box sx={{
        display: 'grid',
        gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' },
        gridTemplateRows: { xs: 'repeat(4, auto)', md: 'repeat(2, auto)' },
        gap: 2
      }}>
        <TextField
          {...register(`group_${groupNumber}.mailingAddress.street`, { required: true })}
          required
          label="Street"
          fullWidth
          error={!!errors?.[`group_${groupNumber}`]?.mailingAddress?.street}
          helperText={errors?.[`group_${groupNumber}`]?.mailingAddress?.street?.message}
        />
        <TextField
          {...register(`group_${groupNumber}.mailingAddress.city`, { required: true })}
          required
          label="City"
          fullWidth
          error={!!errors?.[`group_${groupNumber}`]?.mailingAddress?.city}
          helperText={errors?.[`group_${groupNumber}`]?.mailingAddress?.city?.message}
        />
        <Controller
          name={`group_${groupNumber}.mailingAddress.state`}
          control={control}
          rules={{ required: "State is required" }}
          render={({ field, fieldState: { error } }) => (
            <FormControl error={!!error} fullWidth>
              <InputLabel required>State</InputLabel>
              <Select
                {...field}
                label="State"
              >
                <MenuItem value="" disabled>Select State</MenuItem>
                {states.map((state) => (
                  <MenuItem key={state} value={state}>
                    {state}
                  </MenuItem>
                ))}
              </Select>
              {error && <FormHelperText>{error.message}</FormHelperText>}
            </FormControl>
          )}
        />
        <TextField
          {...register(`group_${groupNumber}.mailingAddress.zip`, { required: true })}
          required
          label="ZIP"
          fullWidth
          error={!!errors?.[`group_${groupNumber}`]?.mailingAddress?.zip}
          helperText={errors?.[`group_${groupNumber}`]?.mailingAddress?.zip?.message}
        />
      </Box>

      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <Checkbox
          onChange={() => setShippingDifferent(!isShippingDifferent)}
          checked={isShippingDifferent}
        />
        <Typography>
          Shipping address is different than mailing?
        </Typography>
      </Box>

      {isShippingDifferent && (
        <>
          <Typography variant="subtitle1" gutterBottom>
            Shipping Address:
          </Typography>

          <Box sx={{
            display: 'grid',
            gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' },
            gridTemplateRows: { xs: 'repeat(4, auto)', md: 'repeat(2, auto)' },
            gap: 2
          }}>
            <TextField
              {...register(`group_${groupNumber}.shippingAddress.street`, { required: isShippingDifferent })}
              required={isShippingDifferent}
              label="Shipping Street"
              fullWidth
              error={!!errors?.[`group_${groupNumber}`]?.shippingAddress?.street}
              helperText={errors?.[`group_${groupNumber}`]?.shippingAddress?.street?.message}
            />
            <TextField
              {...register(`group_${groupNumber}.shippingAddress.city`, { required: isShippingDifferent })}
              required={isShippingDifferent}
              label="Shipping City"
              fullWidth
              error={!!errors?.[`group_${groupNumber}`]?.shippingAddress?.city}
              helperText={errors?.[`group_${groupNumber}`]?.shippingAddress?.city?.message}
            />
            <Controller
              name={`group_${groupNumber}.shippingAddress.state`}
              control={control}
              rules={{ required: isShippingDifferent ? "State is required" : false }}
              render={({ field, fieldState: { error } }) => (
                <FormControl error={!!error} fullWidth>
                  <InputLabel required={isShippingDifferent}>State</InputLabel>
                  <Select
                    {...field}
                    required={isShippingDifferent}
                    label="State"
                  >
                    <MenuItem value="" disabled>Select State</MenuItem>
                    {states.map((state) => (
                      <MenuItem key={state} value={state}>
                        {state}
                      </MenuItem>
                    ))}
                  </Select>
                  {error && <FormHelperText>{error.message}</FormHelperText>}
                </FormControl>
              )}
            />
            <TextField
              {...register(`group_${groupNumber}.shippingAddress.zip`, { required: isShippingDifferent })}
              required={isShippingDifferent}
              label="Shipping ZIP"
              fullWidth
              error={!!errors?.[`group_${groupNumber}`]?.shippingAddress?.zip}
              helperText={errors?.[`group_${groupNumber}`]?.shippingAddress?.zip?.message}
            />
          </Box>
        </>
      )}
    </Box>
  );
}

export default ContactStep;
