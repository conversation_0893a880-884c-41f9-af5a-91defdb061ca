import classNames from "classnames";
import { useState, useEffect, useContext } from "react";
import { AudioRecorder, useAudioRecorder } from "react-audio-voice-recorder";
import { ImBin } from "react-icons/im";
import DebugBreadcrumb from "../TestComponents/DebugBreadcrumb";
import axios from 'axios';
import { observer } from 'mobx-react';
import { v4 as uuidv4 } from 'uuid';

import { AppProviderStore } from "../../AppStore";


const MemoRecorder = ({ memos }) => {

  const { AppStore } = useContext(AppProviderStore);
  const initMemos = memos ? memos : null;
  let trackMemos = AppStore.memos.audioMemos;
  const [urlduh, setURL] = useState("https://yobo.dev");
  const recorderControls = useAudioRecorder({
    autoGainControl: false,
    echoCancellation: false,
    noiseSuppression: false
  },(err) => alert("Recording not allowed"));


  const addAudioElement = async (blob_file) => {
    // let uri = await AppStore.saveBlob(blob_file, 'mp3');
    // let year = new Date().getFullYear()
    console.log('Add Memo')
    console.log(blob_file)
    let fileName = `${year}/judges/${AppStore.judge.name}/${AppStore.currentTrack._id}/`
    // AppStore.addAudioMemo(uri);
    // setURL(uri);
  };

  const handleDeleteAudioMemo = (index) => {
    AppStore.deleteAudioMemo(AppStore.memos.audioMemos[index]);
  };

  return (
    <div className="bg-white">
      <DebugBreadcrumb breadcrumbs={"/Components/WorkViewComponents/MemoRecorder"}/>
      <div className="bg-header px-4 py-3">
        <h2 className="text-white font-semibold">Memos</h2>
      </div>
      <div className="p-4">
        <div id="audio_memos">
          {AppStore.memos.audioMemos && AppStore.memos.audioMemos.length > 0 ? (
            <div className="bg-[#d7d7d7] p-4 mb-4 gap-y-3 flex flex-col">
            {AppStore.memos.audioMemos.map((src, index) => {
                  let memoid = `memo_${index}`;
                  return (
                    <div
                      key={index}
                      className={classNames(
                        "px-4 py-3 rounded-md bg-white flex justify-between items-center gap-4"
                      )}
                    >
                      <div className="flex gap-4 items-center">
                        <label key={memoid} className="text-grayText font-semibold">
                          Memo {index + 1} @{" "}
                        </label>
                        <audio id={memoid} src={src} controls />
                      </div>
                      <button onClick={() => handleDeleteAudioMemo(index)}>
                        <ImBin color="tomato" size={18} />
                      </button>
                    </div>
                  );
                })
            }
            </div>
          ) : (
            <p className="text-iconGreen font-semibold px-4 py-1 w-fit mb-4 rounded-3xl border border-1 border-iconGreen">
              No memos recorded
            </p>
          )}
        </div>
        <div className="flex items-center gap-4">
          <AudioRecorder
            onRecordingComplete={(blob) => {
              addAudioElement(blob);
            }}
            audioTrackConstraints={{
                autoGainControl: false,
                echoCancellation: false,
                noiseSuppression: false
            }} 
            onNotAllowedOrFound={(err) => console.table(err)}  
          />
          <h3 className="text-iconGreen font-semibold">Record Memo</h3>
        </div>
      </div>
    </div>
  );
};

export default observer(MemoRecorder);
