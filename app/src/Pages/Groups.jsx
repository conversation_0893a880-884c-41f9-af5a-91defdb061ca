import { useState, useEffect, useContext, useRef } from "react";
import axios from "axios";
import GroupView from "../Views/GroupView";
import React from "react";
import AppHeader from "../Components/Header/Header";
import GroupSidebar from "../Components/Sidebar/GroupSidebar";
import { AppProviderStore } from "../AppStore";
import { observer } from 'mobx-react';
import { toJS } from "mobx";
import Swal from 'sweetalert2';
import LogRocket from 'logrocket';


function Groups() {
  const { AppStore } = useContext(AppProviderStore);
  const emailRef = useRef(null)
  const [email, setEmail] = useState(undefined);
  const [stop, setStop] = useState(false)
  const {
    selectedGroup, setGroup,
    groups, setGroups,
    menuSelected, setSelectedMenu,
    isBanner, setIsBanner
  } = AppStore;


  useEffect(() => {
    console.log('Selected group', toJS(selectedGroup))
    console.log('Im working here')
    console.log('Selected group Id', toJS(selectedGroup._id))
  }, [selectedGroup])

  useEffect(() => {

    async function getList() {
      try {
        const encodedEmail = encodeURIComponent(email);
        const response = await axios(`/api/getByEmail?email=${encodedEmail}`);
        const data = response.data;
        console.log(data)
        setGroups(data);
        if (groups && groups.length) {
          localStorage.setItem('userGroups', JSON.stringify(groups));
        }
        if (data.length === 1) {
          setGroup(data[0])
        }
        let groupIds = data.map(group => group.group._id).toString()
        console.log('groupIds', groupIds)
        LogRocket.identify(groupIds, {
          email: email
        })
      } catch (error) {
        // Handle the error here
        if (error.response && error.response.status === 401) {
          LogRocket.captureMessage('Invalid email', {
            tags: {
              email: email
            }
          })
          // This assumes that a 404 status means the email was not found
          Swal.fire({
            icon: 'error',
            title: 'Oops...',
            text: 'Email not found. Please enter the email address you used to submit your entry.',
          });
        }
      }
    }

    const savedGroups = localStorage.getItem('userGroups');
    if (savedGroups) {
      setGroups(JSON.parse(savedGroups));
      if (groups.length === 1) {
        setGroup(groups[0])
      }
    }

    if (email) {
      getList();
    }

  }, [email]);

  useEffect(() => {
    if (groups && groups.length) {
      localStorage.setItem('userGroups', JSON.stringify(groups));
    }
  }, [groups]);

  return (
    <>
      <div className="app_contianer">
        {groups.length ? (
          <div className="main_app main_app h-full min-h-[100vh]">
            <AppHeader />
            <div className="grid grid-cols-[270px_1fr] pt-[90px] h-full grid-rows-1 min-h-[100vh]">
              <GroupSidebar />
              <div className="p-5 bg-mainContent main_content overflow-auto">
                {selectedGroup._id ? (
                  <GroupView group={AppStore.selectedGroup} menuSelected={menuSelected} />
                ) : (
                  <div className="bg-white rounded-2xl w-[50%] h-[50%] flex items-center justify-center m-auto mt-[12%] shadow-[0_1px_8px_0px_rgba(170,170,170,1)]">
                    <p className="text-iconGreen font-semibold px-4 py-1 w-fit rounded-3xl border border-1 border-iconGreen">
                      Welcome!
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        ) : (
          <div className="app_loader absolute top-0 left-0 w-full bg-loader h-full bg-cover bg-no-repeat bg-top">
            <div className="bg-loaderOpacity h-full">
              {isBanner ? (
                <div className="w-2/5 xxl:w-[500px] m-auto h-full flex flex-col justify-center">
                  <div className="h-fit shadow-white shadow-xl overflow-hidden rounded-3xl transition-all duration-200 ease-linear hover:scale-[1.1] ">
                    <div className="bg-violin bg-cover bg-no-repeat p-5 pb-36">
                      <div className="rounded-2xl backdrop-blur-3xl bg-transparent p-4 w-fit">
                        <p className="text-white font-semibold">The Foundation</p>
                        <p className="text-iconGreen font-semibold">for Music Education</p>
                      </div>
                    </div>
                    <div className="p-5 w-full bg-iconGreen flex flex-col items-center justify-center text-center">
                      <input
                        ref={emailRef}
                        type="email"
                        placeholder="Enter your email"
                        className="mb-4 p-2 rounded border"
                        onKeyDown={(e) => {
                          if (e.key === "Enter") setEmail(emailRef.current.value)
                        }}
                      />
                      <button
                        className="text-white font-semibold flex items-center"
                        onClick={() => setEmail(emailRef.current.value)}
                      >
                        Submit
                      </button>
                    </div>
                  </div>
                </div>
              ) : (
                <>

                </>
              )}
            </div>
          </div>
        )}

      </div>
    </>
  );
}

export default observer(Groups);
