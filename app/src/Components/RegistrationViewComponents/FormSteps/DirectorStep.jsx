import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Controller } from "react-hook-form";
import {
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  IconButton,
  Tooltip,
  Typography,
  Box,
  Paper,
  Divider
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import CancelIcon from "@mui/icons-material/Cancel";

function DirectorStep({ groupNumber }) {
  const {
    register,
    unregister,
    control,
    watch,
    formState: { errors }
  } = useFormContext();

  const [directors, setDirectors] = useState([{ id: 0 }]); // Array to track directors

  // Get the current directors from the form
  const formDirectors = watch(`group_${groupNumber}.directors`);

  // Initialize directors state based on form data
  useEffect(() => {
    try {
      if (formDirectors && Array.isArray(formDirectors) && formDirectors.length > 0) {
        // Create an array of director objects with IDs
        const directorObjects = formDirectors.map((_, index) => ({ id: index }));
        setDirectors(directorObjects);
      }
    } catch (error) {
      console.error("Error initializing directors:", error);
    }
  }, [formDirectors]);

  const addDirector = () => {
    const newDirectorId = directors.length; // Generate a unique id for the new director
    setDirectors([...directors, { id: newDirectorId }]); // Add a new director
  };

  const removeDirector = (indexToRemove) => {
    // First, get the current values of all directors
    const updatedDirectors = directors.filter((_, index) => index !== indexToRemove);

    // Unregister all director fields to avoid validation issues with removed fields
    for (let i = 0; i < directors.length; i++) {
      unregister([
        `group_${groupNumber}.directors.${i}.name.prefix`,
        `group_${groupNumber}.directors.${i}.name.first`,
        `group_${groupNumber}.directors.${i}.name.last`,
      ]);
    }

    // Update the directors state
    setDirectors(updatedDirectors);
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
      <Typography variant="h6" gutterBottom>
        Director Information
      </Typography>

      <Typography variant="body1" gutterBottom>
        Please provide information about the director(s) for this ensemble.
      </Typography>

      <Divider sx={{ mb: 2 }}>
        <Typography variant="subtitle1" sx={{ fontWeight: 'bold', color: '#008544' }}>
          Directors Information
        </Typography>
      </Divider>

      {directors.map(({ id }, index) => (
        <Paper
          key={id}
          elevation={1}
          sx={{
            p: 2,
            mb: 2,
            position: 'relative',
            borderLeft: '4px solid #008544',
            '&:hover': {
              boxShadow: 3
            }
          }}
        >
          <Box sx={{ position: 'absolute', top: 8, right: 8 }}>
            {index !== 0 && (
              <Tooltip title="Remove Director">
                <IconButton
                  aria-label="Remove Director"
                  size="small"
                  color="error"
                  onClick={() => removeDirector(index)}
                >
                  <CancelIcon />
                </IconButton>
              </Tooltip>
            )}
          </Box>

          <Typography variant="subtitle2" sx={{ mb: 1 }}>
            Director
          </Typography>

          <Box sx={{
            display: 'grid',
            gridTemplateColumns: { xs: '1fr', sm: '0.5fr 1fr 1fr' },
            gap: 2
          }}>
            <Box sx={{ position: 'relative' }}>
              <Controller
                name={`group_${groupNumber}.directors.${index}.name.prefix`}
                defaultValue={""}
                control={control}
                rules={{ required: true }}
                render={({ field: { value, onChange } }) => (
                  <FormControl fullWidth error={!!errors?.[`group_${groupNumber}`]?.directors?.[index]?.name?.prefix}>
                    <InputLabel required id={`directors_${index}.prefix-label`}>
                      Prefix
                    </InputLabel>
                    <Select
                      required
                      labelId={`directors_${index}.prefix-label`}
                      id={`${groupNumber}.directors.${index}.name.prefix-select`}
                      value={value}
                      label="Prefix"
                      onChange={onChange}
                    >
                      <MenuItem value={"Dr."}>Dr.</MenuItem>
                      <MenuItem value={"Mr."}>Mr.</MenuItem>
                      <MenuItem value={"Ms."}>Ms.</MenuItem>
                      <MenuItem value={"Mrs."}>Mrs.</MenuItem>
                      <MenuItem value={"Mx."}>Mx.</MenuItem>
                    </Select>
                  </FormControl>
                )}
              />
              {errors?.[`group_${groupNumber}`]?.directors?.[index]?.name?.prefix && (
                <Typography variant="caption" color="error" sx={{ position: 'absolute', left: 0, bottom: -20 }}>
                  Prefix is required
                </Typography>
              )}
            </Box>

            <Box sx={{ position: 'relative' }}>
              <TextField
                {...register(
                  `group_${groupNumber}.directors.${index}.name.first`,
                  { required: true }
                )}
                required
                fullWidth
                label="First Name"
                error={!!errors?.[`group_${groupNumber}`]?.directors?.[index]?.name?.first}
              />
              {errors?.[`group_${groupNumber}`]?.directors?.[index]?.name?.first && (
                <Typography variant="caption" color="error" sx={{ position: 'absolute', left: 0, bottom: -20 }}>
                  First name is required
                </Typography>
              )}
            </Box>

            <Box sx={{ position: 'relative' }}>
              <TextField
                {...register(
                  `group_${groupNumber}.directors.${index}.name.last`,
                  { required: true }
                )}
                required
                fullWidth
                label="Last Name"
                error={!!errors?.[`group_${groupNumber}`]?.directors?.[index]?.name?.last}
              />
              {errors?.[`group_${groupNumber}`]?.directors?.[index]?.name?.last && (
                <Typography variant="caption" color="error" sx={{ position: 'absolute', left: 0, bottom: -20 }}>
                  Last name is required
                </Typography>
              )}
            </Box>
          </Box>
        </Paper>
      ))}

      {directors.length === 0 && (
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2, fontStyle: 'italic' }}>
          No directors added yet. Click "Add Director" to add one.
        </Typography>
      )}

      <Box sx={{ display: 'flex', justifyContent: 'flex-start', mt: 2 }}>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={addDirector}
          sx={{
            bgcolor: '#008544',
            '&:hover': { bgcolor: '#006834' },
            borderRadius: '20px',
            px: 3
          }}
        >
          Add Director
        </Button>
      </Box>
    </Box>
  );
}

export default DirectorStep;
