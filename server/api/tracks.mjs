import Track from "../models/track.mjs";



export default async function (fastify, opts) {

  //post a track
  fastify.post(`/api/tracks`, async (request, reply) => {
    try {
      const track = request.body;
      console.log(track)
      const newTrack = new Track(track);
      await newTrack.save();
      reply.code(201).send(newTrack._id);
    } catch (err) {
      console.error(err);
      reply.code(500).send(err);
    }
  });

  fastify.put(`/api/tracks/adminUpdate`,async(request,reply)=>{
    let {filter,toUpdate} = {...request.body}
    console.dir(toUpdate,{depth:null})
    let track = await Track.findOneAndUpdate(filter,toUpdate,{new:true}).lean()
    reply.send(track)
  })
  
  fastify.post(`/api/tracks/updateTrack`, async (request, reply) => {
    let track = request.body
    console.log('incoming track update: ')
    console.log(track)
    try {
      await Track.findOneAndUpdate({ _id: track._id }, track)
      reply.code(200);
    } catch (error) {
      console.log(error)
    }
    reply.send(200);
  })

}
