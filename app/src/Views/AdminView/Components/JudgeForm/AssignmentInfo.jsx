import { observer } from 'mobx-react';
import { Controller } from 'react-hook-form';
import { Button, IconButton } from '@mui/material';
import { Cancel } from '@mui/icons-material';
import Select from 'react-select';
import classNames from 'classnames';

const AssignmentInfo = observer(({
    initialFields,
    control,
    watch,
    handleRemove,
    handlePrevStep,
    handleSubmit,
    isEdit,
    categories,
    animatedComponents
}) => {
    const categorySelected = watch(`category0`);
    const handleAssignCategory = (categoryId) => {
        const category = categories.find((cat) => cat._id === categoryId?.value);
        return category ? category.classifications : [];
    };
    const classifications = handleAssignCategory(categorySelected);

    return (
        <div>
            {initialFields.map((field, index) => (
                <div
                    key={field.id}
                    className={classNames([
                        index > 0 && "border-t border-iconGreen pt-8 mt-8",
                        "grid grid-cols-1 md:grid-cols-2 gap-4 relative",
                    ])}
                >
                    {/* Category, Classification, Phase, and Groups fields */}
                    {/* ... Your existing fields code ... */}
                </div>
            ))}

            <div className="flex justify-between items-start !mt-2">
                <Button
                    type="button"
                    onClick={handlePrevStep}
                    variant="outlined"
                    sx={{
                        borderColor: '#22C55E',
                        color: '#22C55E',
                        '&:hover': {
                            borderColor: '#16A34A',
                            color: '#16A34A'
                        }
                    }}
                >
                    Previous Step
                </Button>
                <Button
                    type="submit"
                    variant="contained"
                    sx={{
                        backgroundColor: '#22C55E',
                        '&:hover': {
                            backgroundColor: '#16A34A'
                        }
                    }}
                >
                    {isEdit ? "Save Changes" : "Add Judge"}
                </Button>
            </div>
        </div>
    );
});

export default AssignmentInfo;
