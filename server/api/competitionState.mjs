import competitionState from "../models/competitionState.mjs";


export default async function (fastify,opts){
    fastify.get("/api/competitionState", async function(request,response){
        const compState = await competitionState.findOne()
        response.send(compState);
    })

    fastify.put("/api/competitionState/:key", async function (request, response) {
        try {
            const { key } = request.params; // Extract the key parameter from the request
            // if (key !== "registrationState" && key !== "resultState") {
            //     return response.status(400).send("Invalid key parameter");
            // }

            let compState = await competitionState.findOne();
            if (!compState) {
                return response.status(404).send("State not found");
            }
            if(request.body){
                let {message} = request.body
                compState[key] = message
            }else{
                compState[key] = !compState[key];
            }
            compState = await compState.save();
            console.log("Updated competition state:", compState);
            response.send(compState);
        } catch (error) {
            console.error("Error updating competition state:", error);
            response.status(500).send("Internal Server Error");
        }
    });

 }