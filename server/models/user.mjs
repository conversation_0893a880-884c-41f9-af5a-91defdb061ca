import mongoose from "mongoose";
import bcrypt from 'bcrypt';

const { Schema, model } = mongoose;

const userSchema = new mongoose.Schema({
  prefix: {
    type: String,
    enum: ['Mr', 'Ms', 'Mrs', 'Mx'],
  },
  isAdmin: {
    type: Boolean
  },
  name: {
    first: {
      type: String,
    },
    last: {
      type: String,
    }
  },
  email: {
    type: String,
    required: true,
    unique: true
  },
  cellphone: {
    type: String,
  },
  password: {
    type: String,
    required: true
  },
  schoolName: {
    type: String
  },
  mailingAddress: {
    street: String,
    city: String,
    state: String,
    zip: String
  },
  shippingAddress: {
    street: String,
    city: String,
    state: String,
    zip: String
  },
  groups: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Groups',
    }
  ],
  lastLoggedIn:{
    type:Date
  },
  resetToken:{
    type:String
  },
  resetTokenExpiration:{
    type: Date
  }
});

// Hash the password before saving
userSchema.pre('save', async function (next) {
  const user = this;
  if (!user.isModified('password')) return next();

  const saltRounds = 10;
  try {
    const hashedPassword = await bcrypt.hash(user.password, saltRounds);
    user.password = hashedPassword;
    next();
  } catch (error) {
    return next(error);
  }
});

// Add a method to compare passwords during login
userSchema.methods.comparePassword = async function (candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

const User = mongoose.model('User', userSchema);
export default User;