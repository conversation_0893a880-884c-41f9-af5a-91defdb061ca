/**
 * Pure Function, Do not add side effects
 */

import { observer } from 'mobx-react';
import { useState, useContext, useEffect } from 'react';
import { AppProviderStore } from './../../AppStore'
import YoboMobxDevTools from '../TestComponents/YoboMobxDevtools';
import { FaUserCircle } from "react-icons/fa";
import gravatarUrl from 'gravatar-url';
import { toJS } from "mobx"
import { useNavigate, useHistory, Link } from 'react-router-dom'
import UserProfileModal from '../UserProfileModal/UserProfileModal';

const AppHeader = () => {
  const { AppStore } = useContext(AppProviderStore);
  const navigate = useNavigate();
  const [isAdmin, setIsAdmin] = useState(false);
  const [showAdmin, setShowAdmin] = useState(false);
  const [isUser, setIsUser] = useState(false);
  const [isJudge, setIsJudge] = useState(false);
  const [name, setName] = useState();
  const [profileModalOpen, setProfileModalOpen] = useState(false);

  useEffect(() => {
    console.log('use effect')
  }, [showAdmin])
  const handleLogout = () => {
    console.log('clicked logout')
    localStorage.clear();
    AppStore.reset();
    navigate('/')
  };

  const handleDashboardClick = () => {
    AppStore?.toggleShowAdmin();
    if (AppStore?.showAdmin) {
      navigate("/admin");
    } else {
      navigate("/account");
    }
  };

  const handleProfileClick = () => {
    console.log('Opening user profile modal');
    setProfileModalOpen(true);
  }

  useEffect(() => {
    console.log('location', window.location.href)
    if (AppStore?.judge) {
      setIsJudge(true)
      setName(AppStore.judge.name)
    } else if (AppStore?.user) {
      let username = AppStore.user?.name ? `${AppStore.user.name.first} ${AppStore.user.name.last}` : AppStore.user.email
      setName(username)
      if (AppStore?.user?.isAdmin) {
        // if(AppStore.user.email !== "<EMAIL>"){
        //   setShowAdmin(true)
        //   navigate("/admin")
        // }
        setIsAdmin(true)
      } else {
        setIsUser(true)
      }
    }

  }, [AppStore.user])


  /*   useEffect(() => {
      if (showAdmin) {
        console.log('is admin', showAdmin)
        navigate("/admin");
      } else {
        console.log('is admin', showAdmin)
        navigate("/account");
      }
    },[showAdmin]); */

  return (
    <>
      <div className="bg-header items-center fixed w-full h-[90px] p-5 flex justify-between z-10">
        <h4 className="text-white">
          The Foundation&nbsp;
          <span className="font-medium text-green">For Music Education</span>
        </h4>
        {isJudge ?
          (
            <h4 onClick={() => { AppStore.setJudgeVideoCreated(false); AppStore.setIsBanner(false); }} className='cursor-pointer flex items-center gap-3 font-semibold text-white'>
              {/* <FaUserCircle
            color="#ffffff"
            size={20}
          />  */}
              <img src={AppStore.judge_profile_img} alt="judge" className='w-[30px] h-[30px] rounded-full' />
              {name}
            </h4>
          ) : (
            <div className="flex items-center gap-3 font-semibold text-white">
              <h4 onClick={handleProfileClick} className="cursor-pointer">{name}</h4>
              {isAdmin && <button onClick={() => handleDashboardClick()}>{AppStore.showAdmin ? `View User Dashboard` : `View Admin Dashboard`} </button>}
              {AppStore.user ? <button onClick={handleLogout}>Logout</button> :
                <Link to="/login">
                  <button onClick={handleLogout}>Login</button>
                </Link>
              }
            </div>
          )
        }
      </div>
      {AppStore.user && (
        <UserProfileModal
          open={profileModalOpen}
          handleClose={() => setProfileModalOpen(false)}
        />
      )}
    </>
  );
};
export default observer(AppHeader);
