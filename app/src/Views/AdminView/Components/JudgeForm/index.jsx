
import { useState, useContext, useEffect } from 'react';
import { observer } from 'mobx-react';
import { AppProviderStore } from '../../../../AppStore';
import BasicInfo from './BasicInfo';
import CategoryAssignments from './CategoryAssignments';
import ReviewInfo from './ReviewInfo';

const JudgeForm = observer(({
    initialFields,
    control,
    register,
    watch,
    setValue,
    handleSubmit,
    isEdit,
    categories,
    currentJudge
}) => {
    const { AppStore } = useContext(AppProviderStore);
    const [step, setStep] = useState(1);
    const [judgeForms, setJudgeForms] = useState(initialFields || [{ id: 1 }]);
    const totalSteps = 3;

    useEffect(() => {
        if (isEdit && currentJudge) {
            if (currentJudge.assigned && currentJudge.assigned.length > 0) {
                const newFields = currentJudge.assigned.map((_, index) => ({
                    id: index + 1
                }));
                setJudgeForms(newFields);
            }
        }
    }, [isEdit, currentJudge]);

    const handleNextStep = () => {
        setStep((prev) => Math.min(prev + 1, totalSteps));
        // Scroll to top of the dialog
        const dialogContent = document.querySelector('.MuiDialogContent-root');
        if (dialogContent) {
            dialogContent.scrollTop = 0;
        }
    };

    const handlePrevStep = () => {
        setStep((prev) => Math.max(prev - 1, 1));
        // Scroll to top of the dialog
        const dialogContent = document.querySelector('.MuiDialogContent-root');
        if (dialogContent) {
            dialogContent.scrollTop = 0;
        }
    };

    return (
        <>
            <form onSubmit={handleSubmit}>
                {step === 1 && (
                    <BasicInfo 
                        register={register}
                        handleNextStep={handleNextStep}
                        isEdit={isEdit}
                        currentJudge={currentJudge}
                    />
                )}

                {step === 2 && (
                    <CategoryAssignments
                        control={control}
                        watch={watch}
                        setValue={setValue}
                        register={register}
                        categories={categories}
                        handleNextStep={handleNextStep}
                        handlePrevStep={handlePrevStep}
                        isEdit={isEdit}
                        currentJudge={currentJudge}
                        judgeForms={judgeForms}
                        setJudgeForms={setJudgeForms}
                    />
                )}

                {step === 3 && (
                    <ReviewInfo
                        watch={watch}
                        handlePrevStep={handlePrevStep}
                        handleSubmit={handleSubmit}
                        isEdit={isEdit}
                        categories={categories}
                    />
                )}
            </form>
        </>
    );
});

export default JudgeForm;