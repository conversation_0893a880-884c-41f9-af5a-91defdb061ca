import React from 'react';
import {
    Dialog,
    DialogTitle,
    DialogContent,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Button,
    IconButton
} from '@mui/material';
import { Close, Email } from '@mui/icons-material';
import axios from 'axios';

const SendJudgeEmailModal = ({ open, onClose, showNotification, onOpen }) => {
    const [selectedTemplate, setSelectedTemplate] = React.useState('');

    const handleSendEmails = async () => {
        try {
            await axios.post('/api/sendJudgeEmails', { template: selectedTemplate });
            showNotification('Emails sent successfully');
            onClose();
            setSelectedTemplate('');
        } catch (error) {
            showNotification('Failed to send emails', 'error');
        }
    };

    return (
        <>
            <button
                type="button"
                onClick={onOpen}
                className="mt-4 flex items-center gap-2 px-4 py-3 bg-iconGreen text-white font-semibold rounded-md justify-center hover:bg-green-700 transition"
            >
                <Email className="w-[20px] h-[20px]" />
                Email All Judges
            </button>

            <Dialog
                open={open}
                onClose={onClose}
                maxWidth="sm"
                fullWidth
            >
                <DialogTitle className="text-white font-semibold bg-header px-4 py-3">
                    Send Email to All Judges
                    <IconButton
                        aria-label="close"
                        onClick={onClose}
                        sx={{ position: "absolute", right: 8, top: 8 }}
                    >
                        <Close className="text-white" />
                    </IconButton>
                </DialogTitle>
                <DialogContent className="pt-4">
                    <FormControl fullWidth>
                        <InputLabel>Select Email Template</InputLabel>
                        <Select
                            value={selectedTemplate}
                            onChange={(e) => setSelectedTemplate(e.target.value)}
                            label="Select Email Template"
                        >
                            <MenuItem value="instructions">Instructions and Tutorial</MenuItem>
                            <MenuItem value="reminders">Reminder (not implemented)</MenuItem>
                            <MenuItem value="updates">Updates (not implemented)</MenuItem>
                        </Select>
                    </FormControl>
                    <Button
                        onClick={handleSendEmails}
                        disabled={!selectedTemplate}
                        variant="contained"
                        fullWidth
                        sx={{ mt: 2, color: 'white' }}
                        className="mt-4 flex items-center gap-2 px-4 py-3 bg-iconGreen text-white font-semibold rounded-md justify-center hover:bg-green-700 transition"
                    >
                        Send Emails
                    </Button>
                </DialogContent>
            </Dialog>
        </>
    );
};

export default SendJudgeEmailModal;
