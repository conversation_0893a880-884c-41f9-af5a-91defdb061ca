import { Button, TextField } from '@mui/material';
import { observer } from 'mobx-react';

const BasicInfo = observer(({
    register,
    handleNextStep,
    isEdit,
    currentJudge
}) => {
    return (
        <div className="space-y-6">
            <div className="grid grid-cols-1 gap-4">
                <TextField
                    fullWidth
                    label="Name"
                    variant="outlined"
                    {...register('name')}
                    defaultValue={isEdit ? currentJudge?.name : ''}
                    size="small"
                />
                <TextField
                    fullWidth
                    label="Email"
                    variant="outlined"
                    type="email"
                    {...register('email')}
                    defaultValue={isEdit ? currentJudge?.email : ''}
                    size="small"
                />
            </div>
            <div className="flex justify-end">
                <Button
                    onClick={handleNextStep}
                    variant="contained"
                    color="primary"
                    sx={{
                        backgroundColor: '#00773d', // iconGreen color
                        '&:hover': {
                            backgroundColor: '#357e63' // darker shade for hover
                        }
                    }}
                >
                    Next
                </Button>
            </div>
        </div>
    );
});

export default BasicInfo;