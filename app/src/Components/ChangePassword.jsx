import { useState, useContext, useEffect } from "react";
import { AppProviderStore } from "../AppStore";
import { observer } from 'mobx-react';
import axios from "axios";
import Swal from 'sweetalert2';
import { useNavigate, useParams } from "react-router-dom";
import { toJS } from "mobx";
import { jwtDecode } from "jwt-decode";

function ChangePassword({toggleForm}) {

  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const queryParams = new URLSearchParams(location.search);
  const token = queryParams.get("token");
  const navigate = useNavigate();


  useEffect(()=>{
    console.log('useEffect = ', token)
    if(!token){
      navigate('/')
    }
  },[token])

  const handleChangePassword = async () => {
    if (password !== confirmPassword) {
      Swal.fire({
        icon: 'error',
        title: 'Passwords do not match',
        text: 'Please make sure the passwords match',
      });
      return;
    }

    try {
      // Send request to change password
      const response = await axios.post(`/api/resetPassword/`, {
        newPassword: password,
        resetToken: token
      });
      console.log(response)
      // Handle successful password change
      Swal.fire({
        icon: 'success',
        title: 'Password changed successfully. Log in with your new password',
      });
      navigate('/')
      toggleForm()
    } catch (error) {
      // Handle error
      console.error('Error changing password:', error);
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'An error occurred while changing the password. Please try again later.',
      });
    }
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      handleChangePassword();
    }
  };

  return (
    <div className="p-5 w-full flex flex-col gap-5 items-center justify-center text-center">
      <input
        type="password"
        placeholder="New Password"
        className="p-3 border-0 w-full rounded border"
        value={password}
        onChange={(e) => setPassword(e.target.value)}
      />
      <input
        type="password"
        placeholder="Confirm Password"
        className="p-3 border-0 w-full rounded border"
        value={confirmPassword}
        onChange={(e) => setConfirmPassword(e.target.value)}
        onKeyDown={handleKeyPress}
      />
      <button
        className="p-5 w-1/2 bg-iconGreen text-white font-semibold rounded-3xl"
        onClick={handleChangePassword}
      >
        Change Password
      </button>
    </div>
  );
}

export default observer(ChangePassword);