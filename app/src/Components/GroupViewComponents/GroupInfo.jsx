import { useState, useEffect, useContext, useRef } from "react";
import { AppProviderStore } from "../../AppStore";
import { observer } from "mobx-react";
import IconButton from '@mui/material/IconButton';
import { Print, Edit } from '@mui/icons-material';
import { useReactToPrint } from "react-to-print";
import { Card, CardContent, Typography, Grid, Box, Table, TableHead, TableRow, TableCell, TableBody } from '@mui/material';
import PaymentIcon from '@mui/icons-material/Payment';
import { FaDownload } from "react-icons/fa";
import PrintEntry from './PrintEntry';
import EditModal from "./EditModal";
import { toJS } from "mobx";
import PaymentModal from "./PaymentComponents/PaymentModal";

function GroupInfo() {
  console.log('did I make it here?')
  const { AppStore } = useContext(AppProviderStore);
  const { selectedGroup, trackList, currentTrack, user } = AppStore;
  const [groupClassification, setGroupClassification] = useState({ title: '', classification: '' });
  const [invoiceStatus, setInvoiceStatus] = useState("Not Received");
  const [scoreStatus, setScoreStatus] = useState("Not Received");
  const [open, setOpen] = useState(false);
  const [selectionNumber, setSelectionNumber] = useState(1)
  const [addressString, setAddressString] = useState()
  const [shippingString, setShippingString] = useState()
  const [editing, setEditing] = useState()
  const [soloists, setSoloists] = useState()
  const [paymentOpen, setPaymentOpen] = useState(false);
  const [hasMissingAudioFiles, setHasMissingAudioFiles] = useState(false);


  useEffect(() => {
    if (selectedGroup) {

      let newClassification = '';
      if (selectedGroup.classifications.length) {
        for (const curClassification of selectedGroup.classifications) {
          newClassification += `${curClassification.id.name} | `;
        }
        setGroupClassification({ title: 'Classifications:', classification: newClassification });
      } else if (selectedGroup.classification) {
        setGroupClassification({ title: 'Classification:', classification: selectedGroup.classification.name });
      }

      if (selectedGroup.invoicePaid) {
        setInvoiceStatus("Paid");
      } else {
        setInvoiceStatus('Pending')
      }

      if (selectedGroup.scoresReceived) {
        setScoreStatus("Received");
      } else {
        setScoreStatus("Not Received")
      }

      if (selectedGroup.mailingAddress) {
        setAddressString(`${selectedGroup.mailingAddress?.street} ${selectedGroup.mailingAddress?.city} ${selectedGroup.mailingAddress?.state}, ${selectedGroup.mailingAddress?.zip}`)
      } else {
        setAddressString('Missing')
      }

      if (selectedGroup.shippingAddress) {
        setShippingString(`${selectedGroup.shippingAddress?.street} ${selectedGroup.shippingAddress?.city} ${selectedGroup.shippingAddress?.state}, ${selectedGroup.shippingAddress?.zip}`)
      } else {
        setShippingString('Missing')
      }
    }
  }, [selectedGroup]);

  useEffect(() => {
    console.log('currentTrack useEffect')
    let index = trackList.indexOf(currentTrack)
    setSelectionNumber(index + 1)
    setSoloists(currentTrack.soloists)
  }, [currentTrack])

  // Check if any tracks are missing audio files
  useEffect(() => {
    if (trackList && trackList.length > 0) {
      const missingAudioFiles = trackList.some(track => !track.minioSrc);
      setHasMissingAudioFiles(missingAudioFiles);
    }
  }, [trackList])


  // console.log('Soloists', toJS(currentTrack.soloists), selectedGroup.category.name)
  const componentRef = useRef();
  const handlePrint = useReactToPrint({
    content: () => componentRef.current,
  });

  const handleEditClick = (toEdit) => {
    let editObj = {}


    if (toEdit === 'entry') {
      editObj.title = 'Edit Entry Details';
      editObj.defaultValues = {
        schoolName: selectedGroup.schoolName,
        ensembleName: selectedGroup.ensembleName,
        director: selectedGroup.director,
      }
      editObj.toEdit = { route: 'group', editing: 'entry' }
      editObj.id = selectedGroup._id
    } else if (toEdit === 'currTrack') {

      let choralId = "64345656dd98890aad8cef2d";
      let percussionId = "643456dbdd98890aad8cef30";
      let citationId = "64345c8fdd98890aad8cef3d"
      let hasSoloists = selectedGroup.contest._id !== citationId && selectedGroup.category._id !== choralId && selectedGroup.category._id !== percussionId;

      editObj.title = `Edit ${currentTrack.title} Details`;
      editObj.defaultValues = {
        title: currentTrack.title,
        composer: currentTrack.composer,
        venue: currentTrack.venue,
        dateRecorded: new Date(currentTrack.dateRecorded),
        duration: currentTrack.duration,
        minioSrc: currentTrack.minioSrc,
      }
      if (hasSoloists) {
        editObj.defaultValues.soloists = currentTrack.soloists
      }
      editObj.toEdit = { model: 'track', editing: 'currTrack' }
      editObj.hasSoloists = hasSoloists
      editObj.id = currentTrack._id
    } else if (toEdit === 'contact') {
      editObj.title = 'Edit Contact Info';
      let address = {
        street: '',
        city: '',
        state: '',
        zip: ''
      }
      editObj.defaultValues = {
        cellphone: selectedGroup.cellphone,
        mailingAddress: selectedGroup.mailingAddress ? selectedGroup.mailingAddress : address,
      }
      if (selectedGroup.shippingAddress) {
        editObj.defaultValues.shippingAddress = selectedGroup.shippingAddress
      }
      editObj.toEdit = { model: 'group', editing: 'contact' },
        editObj.id = selectedGroup._id
    }
    setEditing(editObj)
    setOpen(true)
  }

  const handleClose = () => {
    setEditing()
    setOpen(false);
  }

  return (
    <>
      <Grid container spacing={4}>
        <Grid item xs={12} md={6}>
          <Card>
            <Box sx={{ backgroundColor: '#032741', color: 'white', display: 'flex', alignItems: 'center', p: 2 }}>
              <Typography variant="h6" component="div">
                Entry Information
              </Typography>
              <Box sx={{ marginLeft: 'auto', display: 'flex', alignItems: 'center' }}>
                <IconButton color="inherit" onClick={() => handleEditClick('entry')} aria-label="edit">
                  <Edit />
                </IconButton>
                <IconButton color="inherit" onClick={handlePrint} aria-label="print">
                  <Print />
                </IconButton>
              </Box>
            </Box>
            <CardContent>
              <Typography><strong>School Name:</strong> {selectedGroup.schoolName}</Typography>
              <Typography><strong>Ensemble Name:</strong> {selectedGroup.ensembleName}</Typography>
              <Typography><strong>Director:</strong> {selectedGroup.director}</Typography>
              <Typography><strong>Contest:</strong> {selectedGroup.contest.name}</Typography>
              <Typography><strong>Category:</strong> {selectedGroup.category.name}</Typography>
              <Typography><strong>{groupClassification.title}</strong> {groupClassification.classification}</Typography>
              {selectedGroup.invoice && (
                <div className="flex items-center gap-2 my-1">
                  <Typography><strong>Invoice:</strong></Typography>
                  <a
                    href={`/api/generateInvoice/${selectedGroup.invoice}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center text-iconGreen hover:text-[#3b8c6e] transition"
                  >
                    <FaDownload className="mr-1" /> Download
                  </a>
                </div>
              )}
              <Typography><strong>Invoice Status:</strong> {invoiceStatus}</Typography>
              {/* <Typography><strong>Scores Received:</strong> {scoreStatus}</Typography> */}

              {hasMissingAudioFiles && (
                <div className="bg-yellow-50 border-l-4 border-yellow-400 p-3 my-3">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-yellow-700">This entry has tracks missing audio files</p>
                      <p className="text-xs text-yellow-600 mt-1">Check each track and upload the missing recordings before <strong>June 5, {new Date().getFullYear()}</strong></p>
                    </div>
                  </div>
                </div>
              )}

              {!selectedGroup.invoicePaid && (
                <div>
                  <div className="flex justify-center mb-2">
                    <IconButton
                      color="inherit"
                      className="flex items-center p-3 bg-iconGreen text-white font-semibold rounded-lg hover:bg-[#3b8c6e] transition"
                      onClick={() => setPaymentOpen(true)}
                      aria-label="pay"
                    >
                      <span className="mr-2">Pay By Card</span>
                      <PaymentIcon />
                    </IconButton>
                  </div>
                  <div className="text-center text-gray-700 mt-3 p-3 border border-gray-300 rounded-lg bg-gray-50">
                    <p className="font-medium mb-2">You can also pay by check</p>
                    <p className="mb-1">Please mail to:</p>
                    <p className="font-medium">The Foundation for Music Education</p>
                    <p>39 Travis Park Dr.</p>
                    <p>Sugar Land, TX 77479</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <Box sx={{ backgroundColor: '#032741', color: 'white', display: 'flex', alignItems: 'center', p: 2 }}>
              <Typography variant="h6" component="div">
                {currentTrack.title} Details
              </Typography>
              <Box sx={{ marginLeft: 'auto', display: 'flex', alignItems: 'center' }}>
                <IconButton color="inherit" onClick={() => handleEditClick('currTrack')} aria-label="edit">
                  <Edit />
                </IconButton>
              </Box>
            </Box>
            <CardContent>
              {!currentTrack?.minioSrc && (
                <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-yellow-700 font-medium">Missing Audio File</p>
                      <p className="text-xs text-yellow-600 mt-1">This track doesn't have an audio recording uploaded yet. Please upload before <strong>June 5, {new Date().getFullYear()}</strong>.</p>
                      <button
                        onClick={() => handleEditClick('currTrack')}
                        className="mt-2 inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-yellow-700 bg-yellow-100 hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
                      >
                        Upload Audio File
                      </button>
                    </div>
                  </div>
                </div>
              )}
              <Typography><strong>Title:</strong> {currentTrack?.title}</Typography>
              <Typography><strong>Composer:</strong> {currentTrack?.composer}</Typography>
              <Typography><strong>Venue:</strong> {currentTrack?.venue}</Typography>
              <Typography><strong>Date Recorded:</strong> {new Date(currentTrack?.dateRecorded).toLocaleDateString()}</Typography>
              <Typography><strong>Duration:</strong> {currentTrack?.duration}</Typography>
              {currentTrack.soloists.length && selectedGroup.category.name !== "Citation of Excellence" ? (
                <Box>
                  <Typography><strong>Soloists:</strong></Typography>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell><strong>Name</strong></TableCell>
                        <TableCell><strong>Instrument</strong></TableCell>
                        <TableCell><strong>Time Stamp</strong></TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {currentTrack.soloists.map((soloist, index) => (
                        <TableRow key={index}>
                          <TableCell>{soloist.name}</TableCell>
                          <TableCell>{soloist.instrument}</TableCell>
                          <TableCell>{soloist?.timeStamp ? soloist.timeStamp : 'N/A'}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </Box>
              ) : null}
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <Box sx={{ backgroundColor: '#032741', color: 'white', display: 'flex', alignItems: 'center', p: 2 }}>
              <Typography variant="h6" component="div">
                Contact Information
              </Typography>
              <Box sx={{ marginLeft: 'auto', display: 'flex', alignItems: 'center' }}>
                <IconButton color="inherit" onClick={() => handleEditClick('contact')} aria-label="edit">
                  <Edit />
                </IconButton>
              </Box>
            </Box>
            <CardContent>
              <Typography><strong>Email:</strong> {selectedGroup.email}</Typography>
              <Typography><strong>Phone:</strong> {selectedGroup.cellphone}</Typography>
              {selectedGroup?.shippingAddress ? (
                <>
                  <Typography><strong>Mailing Address:</strong> {addressString} </Typography>
                  <Typography><strong>Shipping Address:</strong> {shippingString} </Typography>
                </>

              ) :
                <Typography><strong>Mailing/Shipping Address:</strong> {addressString} </Typography>
              }
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      <EditModal open={open} handleClose={handleClose} editing={editing} userId={user._id} />
      <PrintEntry ref={componentRef} selectedGroup={selectedGroup} addressString={addressString} shippingString={shippingString} groupClassification={groupClassification} />
      <PaymentModal open={paymentOpen} handleClose={() => setPaymentOpen(false)} />

    </>
  );
}

export default observer(GroupInfo);
