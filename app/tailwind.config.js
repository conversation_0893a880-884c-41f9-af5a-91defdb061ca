/** @type {import('tailwindcss').Config} */

module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
    "./app/**/*.{js,ts,jsx,tsx}", // Note the addition of the `app` directory.
    "./pages/**/*.{js,ts,jsx,tsx}",
    "./components/**/*.{js,ts,jsx,tsx}",
    "./utils/**/*",
    "./constants/**/*",
    "./styles/**/*",
  ],
  theme: {
    letterSpacing: {
      1: "0em",
      2: "0.025em",
      3: "0.05em",
      4: "0.15em",
    },
    screens: {
      xs: "760px",
      sm: "920px",
      md: "1080px",
      lg: "1140px",
      xl: "1280px",
      xxl: "1600px",
    },
    colors: {
      // basic colors
      white: "#ffffff",
      current: "currentColor",
      transparent: "transparent",

      header: "#032741",
      sidebar: "#0d3d75",
      sidebarListItem: "#3b8c6e",
      green: "#56d395",
      grayText: "#7f889d",
      iconGreen: "#008544",
      mainContent: "#eeeeee",
      // theme colors
      black: "#1f1c1f",
      loaderOpacity: "#005b5999",
      gray: "#6b636b",
      dullGray: "#edeaed",
      borderColor: "#d2d2d2",
      listBorderColor: "#e7e6e7",
      red: "#ff0000",
    },
    fontFamily: {
      sans: ["Poppins", "sans-serif"],
    },
    extend: {
      backgroundImage: {
        violin: "url('/public/assets/images/violin.jpg')",
        loader: "url('/public/assets/images/fme_loader.png')",
      },
    },
  },
  corePlugins: { float: false },
  important: true,
  plugins: [require("@tailwindcss/line-clamp")],
};
