import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Chip,
  LinearProgress
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Assignment as AssignmentIcon,
  Group as GroupIcon,
  Flag as FlagIcon
} from '@mui/icons-material';

const ScoringProgressCard = ({ totalGroups, scoredGroups, progress, phase, movesOnGroups }) => {
  return (
    <Paper sx={{ p: 3, mb: 4, borderRadius: 2, boxShadow: 2 }}>
      <Typography variant="h6" sx={{ mb: 2, color: '#3b8c6e', display: 'flex', alignItems: 'center' }}>
        <AssignmentIcon sx={{ mr: 1 }} /> Classification Scoring Progress
      </Typography>

      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 2 }}>
        <Chip
          icon={<GroupIcon />}
          label={`${totalGroups} Total Groups`}
          size="medium"
          sx={{ borderColor: '#3b8c6e', color: '#3b8c6e', fontWeight: 'bold' }}
          variant="outlined"
        />
        <Chip
          icon={<CheckCircleIcon />}
          label={`${scoredGroups} Scored`}
          size="medium"
          sx={{ borderColor: '#3b8c6e', color: '#3b8c6e', fontWeight: 'bold' }}
          variant="outlined"
        />
        <Chip
          icon={<AssignmentIcon />}
          label={`${totalGroups - scoredGroups} Remaining`}
          size="medium"
          sx={{
            borderColor: (totalGroups - scoredGroups) > 0 ? '#ff9800' : '#3b8c6e',
            color: (totalGroups - scoredGroups) > 0 ? '#ff9800' : '#3b8c6e',
            fontWeight: 'bold'
          }}
          variant="outlined"
        />
        {phase === 1 && (
          <Chip
            icon={<FlagIcon />}
            label={`${movesOnGroups} Move to Phase 2`}
            size="medium"
            sx={{
              borderColor: '#3f51b5',
              color: '#3f51b5',
              fontWeight: 'bold'
            }}
            variant="outlined"
          />
        )}
      </Box>

      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <Typography variant="body1" sx={{ mr: 2, minWidth: '120px', fontWeight: 'bold' }}>
          Scoring Progress:
        </Typography>
        <Box sx={{ flexGrow: 1 }}>
          <LinearProgress
            variant="determinate"
            value={progress}
            sx={{
              height: 10,
              borderRadius: 5,
              backgroundColor: 'rgba(59, 140, 110, 0.2)',
              '& .MuiLinearProgress-bar': {
                backgroundColor: '#3b8c6e',
              }
            }}
          />
        </Box>
        <Typography variant="body1" sx={{ ml: 2, fontWeight: 'bold' }}>
          {Math.round(progress)}%
        </Typography>
      </Box>
    </Paper>
  );
};

export default ScoringProgressCard;
