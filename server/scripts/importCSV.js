const csv = require("csvtojson");
const axios = require("axios");
const csvFile = "./tfme-forms.csv";

async function fetchByName(toFetch, name) {
  let route = `/api/${toFetch}?name=${name}`;
  if(Array.isArray(name)){
    let routes = name.map(n=>{
      return `/api/${toFetch}?name=${n}`
    })
    try{
      let responses = await axios.all(routes.map((route)=> axios.get(route)))
      const responseData = responses.map(response => response.data)
      console.log(responseData)
      return responseData
    }catch(err){
      console.log(err)
    }
  } else{
    try {
      let res = await axios.get(route);
      return res.data;
    } catch (err) {
      console.log("error on fetchByName", toFetch, name);
    }
  }

}
async function createTracks(tracks) {
  let memos = {
    audioMemos: [],
    notes: [],
    soloistNotes: [],
  };
  let idArray = [];
  for (const track of tracks) {
    track.memos = memos;
    let res = await axios.post(`/api/tracks`, track);
    idArray.push(res.data);
  }
  return idArray;
}
function assignMask(groupCount) {
  let mask = "";
  while (groupCount > 0) {
    groupCount--; // Adjust count to start from 0 instead of 1
    mask = String.fromCharCode(65 + (groupCount % 26)) + mask;
    groupCount = Math.floor(groupCount / 26);
  }
  //console.log(mask)
  return mask;
}
async function createGroups(groups) {
  let categories = {
    "64345656dd98890aad8cef2d": {
      "64345155dd98890aad8cef15": 0,
      "643451b4dd98890aad8cef16": 0,
      "643451f9dd98890aad8cef17": 0,
      "6434523bdd98890aad8cef18": 0,
      "64381e4a1e703527f8f9ae15": 0,
    },
    "643456dbdd98890aad8cef2e": {
      "6434524add98890aad8cef19": 0,
      "643453f8dd98890aad8cef1a": 0,
      "64381ea61e703527f8f9ae16": 0,
    },
    "643456dbdd98890aad8cef2f": {
      "643453f8dd98890aad8cef1b": 0,
      "643453f8dd98890aad8cef1c": 0,
      "643453f8dd98890aad8cef1d": 0,
      "643453f8dd98890aad8cef1e": 0,
      "643453f8dd98890aad8cef1f": 0,
    },
    "643456dbdd98890aad8cef30": {
      "6434524add98890aad8cef19": 0,
      "643453f8dd98890aad8cef1a": 0,
    },
    "643456dbdd98890aad8cef31": {
      "643453f8dd98890aad8cef20": 0,
      "643453f8dd98890aad8cef21": 0,
      "643453f8dd98890aad8cef22": 0,
      "643453f8dd98890aad8cef23": 0,
      "643453f8dd98890aad8cef24": 0,
      "643453f8dd98890aad8cef25": 0,
      "643453f8dd98890aad8cef26": 0,
    },
    "643456dbdd98890aad8cef32": {
      "643453f8dd98890aad8cef23": 0,
      "643453f8dd98890aad8cef24": 0,
      "643453f8dd98890aad8cef20": 0,
      "643453f8dd98890aad8cef21": 0,
    },
    "643456dbdd98890aad8cef33": {
      "643453f8dd98890aad8cef1b": 0,
      "643453f8dd98890aad8cef1c": 0,
      "643453f8dd98890aad8cef1e": 0,
      "643453f8dd98890aad8cef1d": 0,
    },
  };

  for (const group of groups) {
    categories[group.category][group.classification] += 1;
    group.maskedName = assignMask(
      categories[group.category][group.classification]
    );
    try {
      await axios.post(`/api/groups`, group);
    } catch (err) {
      console.log(err);
    }
  }
}
async function imports() {
  const entries = await csv().fromFile(csvFile);
  let groups = [];
  for (const entry of entries) {
    let tracks = [
      {
        title: entry.cd1Selection,
        composer: entry.cd1Composer,
        venue: entry.cd1Venue,
        trackSrc: entry.recording_1,
      },
    ];
    if (entry.cd2Selection !== "") {
      tracks.push({
        title: entry.cd2Selection,
        composer: entry.cd2Composer,
        venue: entry.cd2Venue,
        trackSrc: entry.recording_2,
      });
    }
    if (entry.cd3Selection !== "") {
      tracks.push({
        title: entry.cd3Selection,
        composer: entry.cd3Composer,
        venue: entry.cd3Venue,
        trackSrc: entry.recording_3,
      });
    }
    let classification =
      entry.ContestCategory !== ""
        ? entry.ContestCategory
        : entry.EntryClassification;
    if(classification != "New Music Division" && entry.AdditionalEntryClassification !== ""){
      let schoolName = entry.SchoolName.toLowerCase()
      let newDivision = ""
      if(
        schoolName.includes("middle school") ||
        schoolName.includes("ms") || 
        schoolName.includes("junior high") || 
        schoolName.includes("jh")
      ){
        newDivision = "New Music Division Middle School";
      }
      classification = [entry.EntryClassification, newDivision]
    }
    let contest = entry.formType.includes("Non")
      ? "Citation Of Excellence"
      : "Mark Of Excellence";
    let category = entry.formType;
    if (contest === "Citation Of Excellence") {
      category = category.replace(/\bNon-Varsity\b/g, "").trim();
    }
    if (category === "National Percussion Ensemble Honors") {
      category = "National Percussion Honors"
    }
    if(!Array.isArray(classification)){
      switch (classification) {
        case "HS full orchestra":
          classification = "High School Full";
          break;
        case "HS string orchestra":
          classification = "High School String";
        case "MS full orchestra":
          classification = "Middle School Full";
          break;
        case "MS string orchestra":
          classification = "Middle School String";
          break;
        case "Middle School Treble Choir":
          classification = "Middle School Treble";
          break;
        case "String Orchestra - MS":
          classification = "Middle School String";
          break;
        case "String Orchestra - HS":
          classification = "High School String";
          break;
        case "Full Orchestra - MS":
          classification = "Middle School Full";
          break;
        case "Full Orchestra - HS":
          classification = "High School Full";
          break;
        case "High School Jazz Ensemble":
        case "High School Percussion Ensemble":
          classification = "High School";
          break;
        case "Middle School Jazz Ensemble":
        case "Middle School Percussion Ensemble":
          classification = "Middle School";
          break;
        case "High School Mixed Choir":
          classification = "High School Mixed";
          break;
        case "Middle School Treble Choir":
          classification = "Middle School Treble";
        case "New Music Division":
          let schoolName = entry.SchoolName.toLowerCase();
          if (
            schoolName.includes("middle school") ||
            schoolName.includes("ms") ||
            schoolName.includes("junior high") ||
            schoolName.includes("jh")
          ) {
            classification = "New Music Division Middle School";
          } else {
            classification = "New Music Division High School";
          }
        default:
          break;
      }
    }

    let group = {
      schoolName: entry.SchoolName,
      ensembleName: entry.EnsembleName,
      director: entry.DirectorName,
      contest: await fetchByName("contests", contest),
      category: await fetchByName("categories", category),
      classification: await fetchByName("classifications", classification),
      tracks: await createTracks(tracks),
    };
    groups.push(group);
  }
  await createGroups(groups);
}

imports();
