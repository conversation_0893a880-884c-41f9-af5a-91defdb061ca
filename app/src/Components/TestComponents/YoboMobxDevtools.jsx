import { Fragment, useContext,useState } from "react";
import { AppProviderStore } from "../../AppStore";
import { JsonViewer } from '@textea/json-viewer'
import { observer } from 'mobx-react';

const YoboMobxDevTools = ({children}) => {
  const draw_debug = false;
  return(
    draw_debug ? <Fragment>
    <YoboMobxDevSidebar />
    {children}
</Fragment>: 
    <Fragment>
        {children}
    </Fragment>
  )
};

const YoboMobxDevSidebar = () => {
    const { AppStore } = useContext(AppProviderStore);

    let [showDevSidebar, setShowDevSidebar] = useState(true);

    let style = {
    width: '300px',
    backgroundColor:'pink', 
    position:'absolute', 
    left:'0', 
    zIndex:'999', 
    minHeight:'1000px'};


    return (showDevSidebar ? <div style={style}>
        <button onClick={()=>{setShowDevSidebar(false)}}>Hide</button>
        <h1>dev sidebar</h1>
        <JsonViewer value={AppStore} />
        </div> : <button style={{zIndex:999, position:'absolute', left:'0'}} onClick={()=>{setShowDevSidebar(true)}}>Show</button>)
}

export default YoboMobxDevTools;
