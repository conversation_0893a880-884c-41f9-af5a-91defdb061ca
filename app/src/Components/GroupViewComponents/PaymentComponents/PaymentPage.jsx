import React, { useState, useEffect, useContext, useRef } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';
import CheckoutForm from './CheckoutForm';
import { observer } from 'mobx-react';
import { AppProviderStore } from "../../../AppStore";
import axios from 'axios';

const stripePromise = loadStripe(process.env.REACT_APP_STRIPE_PUBLIC_KEY);


const PaymentPage = observer(({handleClose}) => {
  const { AppStore } = useContext(AppProviderStore);
  const { selectedGroup, fetchInvoiceDetails, user } = AppStore;
  const [invoice, setInvoice] = useState();
  const [clientSecret, setClientSecret] = useState(null);
  const paymentIntentCreated = useRef(false);
  const isInitialMount = useRef(true);

  useEffect(() => {
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    async function initializePayment() {
      if (!selectedGroup || paymentIntentCreated.current || clientSecret) return;

      try {
        // Set flag before starting the process
        paymentIntentCreated.current = true;

        // Fetch invoice details
        const invoiceDetails = await fetchInvoiceDetails(selectedGroup.invoice);
        setInvoice(invoiceDetails);

        // Create payment intent if we have all required data
        if (invoiceDetails && user) {
          const amountInCents = Math.round(invoiceDetails.total * 100);
          const response = await axios.post('/api/payment_intent', {
            amount: amountInCents,
            userEmail: user.email,
            schoolName: selectedGroup.schoolName,
            invoiceNumber: invoiceDetails.invoiceNo,
            invoiceId: selectedGroup.invoice,
          });

          if (response.data.clientSecret) {
            setClientSecret(response.data.clientSecret);
          } else {
            // Reset flag if we didn't get a client secret
            paymentIntentCreated.current = false;
            throw new Error('No clientSecret returned');
          }
        }
      } catch (err) {
        // Reset flag on error
        paymentIntentCreated.current = false;
        console.error('Error initializing payment:', err);
      }
    }

    initializePayment();

    // Cleanup function
    return () => {
      paymentIntentCreated.current = false;
    };
  }, [selectedGroup, user, fetchInvoiceDetails, clientSecret]);

  if (!selectedGroup || !invoice) return <p>Loading...</p>;

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <div className="flex justify-between mb-6">
        <div className="w-1/2 pr-6">
          <div className="text-center mb-6">
            <p className="text-sm text-gray-500">
              Invoice No: <strong>{invoice.invoiceNo}</strong> | Due Date: {new Date(invoice.invoiceDue).toLocaleDateString()}
            </p>
          </div>

          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-800">Bill To</h3>
            <p className="text-gray-600">{invoice.billTo}</p>
            <p className="text-gray-600"><strong>Invoice Date:</strong> {new Date(invoice.invoiceDate).toLocaleDateString()}</p>
          </div>

          <table className="w-full mb-6 table-auto border-collapse">
            <thead>
              <tr>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-600">Qty</th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-600">Description</th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-600">Price</th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-600">Total</th>
              </tr>
            </thead>
            <tbody>
              {invoice.lineItems.map((item) => (
                <tr key={item._id} className="border-t">
                  <td className="px-4 py-2 text-sm text-gray-700">{item.qty}</td>
                  <td className="px-4 py-2 text-sm text-gray-700">{item.description}</td>
                  <td className="px-4 py-2 text-sm text-gray-700">${item.price.toFixed(2)}</td>
                  <td className="px-4 py-2 text-sm text-gray-700">${item.total.toFixed(2)}</td>
                </tr>
              ))}
            </tbody>
          </table>

          <div className="flex justify-between items-center mb-6">
            <h3 className="text-xl font-semibold text-gray-800">Total Due</h3>
            <p className="text-xl font-bold text-blue-600">${invoice.total.toFixed(2)}</p>
          </div>
        </div>

        <div className="w-1/2 pl-6">
          {clientSecret && (
            <Elements 
              stripe={stripePromise} 
              options={{
                clientSecret,
                appearance: {
                  theme: 'stripe',
                  variables: {
                    colorPrimary: '#008544',
                  },
                },
                layout: {
                  type: 'tabs',
                  defaultCollapsed: false,
                },
              }}
            >
              <CheckoutForm 
                amount={invoice.total} 
                invoiceNo={invoice.invoiceNo} 
                clientSecret={clientSecret}
                handleClose={handleClose} 
              />
            </Elements>
          )}
        </div>
      </div>
    </div>
  );
});

export default PaymentPage;
