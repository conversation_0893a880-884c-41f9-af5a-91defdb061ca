import { useState, useEffect, useContext } from "react";
import AdminView from "../Views/AdminView";
import AppHeader from "../Components/Header/Header";
import AdminSidebar from "../Components/AdminSidebar";
import { observer } from 'mobx-react';
import { AppProviderStore } from "../AppStore";
import { useNavigate } from "react-router-dom";
import { toJS } from "mobx";
function Admin() {
  //will need to add a login
  const { AppStore } = useContext(AppProviderStore);
  const {
    user,
    getGroups,
    groups,
    getContests,
    getCategories,
    contests,
    getJudges,
    judges,
    getCompetitionState,
    setCompetitionState,
    getUsers
  } = AppStore;

  const navigate = useNavigate();

  useEffect(()=>{
    console.log('admin panel', toJS(user))
    if(!user || !user.isAdmin){
      console.log('navigate')
      navigate('/')
    }
  },[user,navigate]);



  const menuOptions = [
    {
      title: "Contests",
      route: "contests",
    },
    {
      title: "Judges",
      route: "judges",
    },
    {
      title: "Entries",
      route: "groups"
    },
    {
      title: "Users",
      route: "users"
    },
    {
      title: "Results",
      route:"results",
    },
    // {
    //   title: "Settings",
    // },
    {
      title: "Download Codes",
    }
  ];
  const [selectedOption, setSelected] = useState();

  async function handleSelect(option) {
    setSelected(option);
  }
  useEffect(() => {
    handleSelect({ title: 'Contests', route: 'contests' });
  }, []);
  useEffect(() => {
    getCompetitionState();
    getGroups();
    getContests();
    getCategories()
    getJudges();
  }, []);



  console.log("Judges: ", toJS(judges))
  return (
    <>
      <div className="app_container">
        <div className="main_app h-full min-h-[100vh]">
          <AppHeader />
          <div className="grid grid-cols-[270px_1fr] pt-[90px] h-full grid-rows-1 min-h-[100vh]">
            <AdminSidebar
              menuOptions={menuOptions}
              handleSelect={handleSelect}
              selectedOption={selectedOption}
            />
            <div className="p-5 bg-mainContent main_content overflow-auto">
              {selectedOption && <AdminView option={selectedOption}
                data={selectedOption?.route === "groups" ? JSON?.parse(JSON?.stringify(groups))
                  : selectedOption?.route === "contests" ? JSON?.parse(JSON?.stringify(contests))
                    : selectedOption?.route === "judges" && JSON?.parse(JSON?.stringify(judges))} />
              }
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
export default observer(Admin);
