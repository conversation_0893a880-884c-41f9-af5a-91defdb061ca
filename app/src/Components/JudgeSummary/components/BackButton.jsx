import React from 'react';
import { <PERSON>, Button } from '@mui/material';
import { ArrowBack as ArrowBackIcon } from '@mui/icons-material';

const BackButton = ({ onClick, text }) => (
  <Box sx={{ p: 1 }}>
    <Button
      startIcon={<ArrowBackIcon />}
      onClick={onClick}
      sx={{
        color: '#3b8c6e',
        '&:hover': {
          backgroundColor: 'rgba(59, 140, 110, 0.1)',
        },
      }}
    >
      {text}
    </Button>
  </Box>
);

export default BackButton;
